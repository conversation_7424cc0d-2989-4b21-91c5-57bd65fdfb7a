from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Report(db.Model):
    __tablename__ = 'reports'
    
    id = db.Column(db.Integer, primary_key=True)
    evaluation_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON>('evaluations.id'), nullable=False)
    generated_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    report_type = db.Column(db.Enum('individual', 'department', 'summary', name='report_types'), default='individual')
    file_path = db.Column(db.String(255))
    file_name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert report object to dictionary"""
        return {
            'id': self.id,
            'evaluation_id': self.evaluation_id,
            'generated_by': self.generated_by,
            'generator_name': self.generator.full_name if self.generator else None,
            'report_type': self.report_type,
            'file_path': self.file_path,
            'file_name': self.file_name,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'employee_name': self.evaluation.employee.full_name if self.evaluation and self.evaluation.employee else None,
            'evaluation_period': {
                'start': self.evaluation.evaluation_period_start.isoformat() if self.evaluation and self.evaluation.evaluation_period_start else None,
                'end': self.evaluation.evaluation_period_end.isoformat() if self.evaluation and self.evaluation.evaluation_period_end else None
            } if self.evaluation else None
        }
    
    def get_download_url(self):
        """Get download URL for the report"""
        return f'/api/reports/{self.id}/download'
    
    def __repr__(self):
        return f'<Report {self.id}: {self.file_name}>'
