[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Dashboard.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Navigation.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\EvaluationForm.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Login.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Reports.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\EvaluationResults.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\EmployeeManagement.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\contexts\\AuthContext.js": "10"}, {"size": 626, "mtime": 1758976402892, "results": "11", "hashOfConfig": "12"}, {"size": 2314, "mtime": 1758976425536, "results": "13", "hashOfConfig": "12"}, {"size": 8874, "mtime": 1758978563928, "results": "14", "hashOfConfig": "12"}, {"size": 1349, "mtime": 1758977389490, "results": "15", "hashOfConfig": "12"}, {"size": 20565, "mtime": 1758977260009, "results": "16", "hashOfConfig": "12"}, {"size": 3742, "mtime": 1758976977772, "results": "17", "hashOfConfig": "12"}, {"size": 11990, "mtime": 1758977363651, "results": "18", "hashOfConfig": "12"}, {"size": 17818, "mtime": 1758977321652, "results": "19", "hashOfConfig": "12"}, {"size": 13068, "mtime": 1758977049668, "results": "20", "hashOfConfig": "12"}, {"size": 2192, "mtime": 1758976447344, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1o6hi70", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\App.js", ["52", "53"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Dashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Navigation.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\EvaluationForm.js", ["54"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Login.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\Reports.js", ["55", "56"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\EvaluationResults.js", ["57", "58"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\components\\EmployeeManagement.js", ["59"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\src\\contexts\\AuthContext.js", [], [], {"ruleId": "60", "severity": 1, "message": "61", "line": 1, "column": 17, "nodeType": "62", "messageId": "63", "endLine": 1, "endColumn": 25}, {"ruleId": "60", "severity": 1, "message": "64", "line": 1, "column": 27, "nodeType": "62", "messageId": "63", "endLine": 1, "endColumn": 36}, {"ruleId": "65", "severity": 1, "message": "66", "line": 44, "column": 6, "nodeType": "67", "endLine": 44, "endColumn": 121, "suggestions": "68"}, {"ruleId": "65", "severity": 1, "message": "69", "line": 22, "column": 6, "nodeType": "67", "endLine": 22, "endColumn": 28, "suggestions": "70"}, {"ruleId": "60", "severity": 1, "message": "71", "line": 102, "column": 9, "nodeType": "62", "messageId": "63", "endLine": 102, "endColumn": 23}, {"ruleId": "60", "severity": 1, "message": "72", "line": 17, "column": 10, "nodeType": "62", "messageId": "63", "endLine": 17, "endColumn": 20}, {"ruleId": "65", "severity": 1, "message": "73", "line": 39, "column": 6, "nodeType": "67", "endLine": 39, "endColumn": 44, "suggestions": "74"}, {"ruleId": "65", "severity": 1, "message": "75", "line": 33, "column": 6, "nodeType": "67", "endLine": 33, "endColumn": 51, "suggestions": "76"}, "no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculateScores'. Either include it or remove the dependency array.", "ArrayExpression", ["77"], "React Hook useEffect has a missing dependency: 'fetchReports'. Either include it or remove the dependency array.", ["78"], "'formatFileSize' is assigned a value but never used.", "'pagination' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchEvaluations'. Either include it or remove the dependency array.", ["79"], "React Hook useEffect has a missing dependency: 'fetchEmployees'. Either include it or remove the dependency array.", ["80"], {"desc": "81", "fix": "82"}, {"desc": "83", "fix": "84"}, {"desc": "85", "fix": "86"}, {"desc": "87", "fix": "88"}, "Update the dependencies array to be: [formData.total_work_days, formData.present_days, formData.late_arrivals, formData.violations_count, autoCalculate, calculateScores]", {"range": "89", "text": "90"}, "Update the dependencies array to be: [currentPage, fetchReports, filters]", {"range": "91", "text": "92"}, "Update the dependencies array to be: [currentPage, fetchEvaluations, filters, location.state]", {"range": "93", "text": "94"}, "Update the dependencies array to be: [currentPage, fetchEmployees, searchTerm, selectedDepartment]", {"range": "95", "text": "96"}, [1200, 1315], "[formData.total_work_days, formData.present_days, formData.late_arrivals, formData.violations_count, autoCalculate, calculateScores]", [637, 659], "[currentPage, fetchReports, filters]", [1387, 1425], "[currentPage, fetchEvaluations, filters, location.state]", [1080, 1125], "[currentPage, fetchEmployees, searchTerm, selectedDepartment]"]