{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "Table", "forwardRef", "bsPrefix", "className", "striped", "bordered", "borderless", "hover", "size", "variant", "responsive", "props", "ref", "decoratedBsPrefix", "classes", "table", "responsiveClass", "children", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/Table.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Table = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  striped,\n  bordered,\n  borderless,\n  hover,\n  size,\n  variant,\n  responsive,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'table');\n  const classes = classNames(className, decoratedBsPrefix, variant && `${decoratedBsPrefix}-${variant}`, size && `${decoratedBsPrefix}-${size}`, striped && `${decoratedBsPrefix}-${typeof striped === 'string' ? `striped-${striped}` : 'striped'}`, bordered && `${decoratedBsPrefix}-bordered`, borderless && `${decoratedBsPrefix}-borderless`, hover && `${decoratedBsPrefix}-hover`);\n  const table = /*#__PURE__*/_jsx(\"table\", {\n    ...props,\n    className: classes,\n    ref: ref\n  });\n  if (responsive) {\n    let responsiveClass = `${decoratedBsPrefix}-responsive`;\n    if (typeof responsive === 'string') {\n      responsiveClass = `${responsiveClass}-${responsive}`;\n    }\n    return /*#__PURE__*/_jsx(\"div\", {\n      className: responsiveClass,\n      children: table\n    });\n  }\n  return table;\n});\nTable.displayName = 'Table';\nexport default Table;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACRC,SAAS;EACTC,OAAO;EACPC,QAAQ;EACRC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC,OAAO;EACPC,UAAU;EACV,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,iBAAiB,GAAGhB,kBAAkB,CAACK,QAAQ,EAAE,OAAO,CAAC;EAC/D,MAAMY,OAAO,GAAGnB,UAAU,CAACQ,SAAS,EAAEU,iBAAiB,EAAEJ,OAAO,IAAI,GAAGI,iBAAiB,IAAIJ,OAAO,EAAE,EAAED,IAAI,IAAI,GAAGK,iBAAiB,IAAIL,IAAI,EAAE,EAAEJ,OAAO,IAAI,GAAGS,iBAAiB,IAAI,OAAOT,OAAO,KAAK,QAAQ,GAAG,WAAWA,OAAO,EAAE,GAAG,SAAS,EAAE,EAAEC,QAAQ,IAAI,GAAGQ,iBAAiB,WAAW,EAAEP,UAAU,IAAI,GAAGO,iBAAiB,aAAa,EAAEN,KAAK,IAAI,GAAGM,iBAAiB,QAAQ,CAAC;EACxX,MAAME,KAAK,GAAG,aAAahB,IAAI,CAAC,OAAO,EAAE;IACvC,GAAGY,KAAK;IACRR,SAAS,EAAEW,OAAO;IAClBF,GAAG,EAAEA;EACP,CAAC,CAAC;EACF,IAAIF,UAAU,EAAE;IACd,IAAIM,eAAe,GAAG,GAAGH,iBAAiB,aAAa;IACvD,IAAI,OAAOH,UAAU,KAAK,QAAQ,EAAE;MAClCM,eAAe,GAAG,GAAGA,eAAe,IAAIN,UAAU,EAAE;IACtD;IACA,OAAO,aAAaX,IAAI,CAAC,KAAK,EAAE;MAC9BI,SAAS,EAAEa,eAAe;MAC1BC,QAAQ,EAAEF;IACZ,CAAC,CAAC;EACJ;EACA,OAAOA,KAAK;AACd,CAAC,CAAC;AACFf,KAAK,CAACkB,WAAW,GAAG,OAAO;AAC3B,eAAelB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}