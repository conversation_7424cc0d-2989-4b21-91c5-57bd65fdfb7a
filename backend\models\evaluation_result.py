from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class EvaluationResult(db.Model):
    __tablename__ = 'evaluation_results'
    
    id = db.Column(db.Integer, primary_key=True)
    evaluation_id = db.Column(db.<PERSON><PERSON>, db.<PERSON><PERSON>('evaluations.id'), nullable=False)
    
    # Fuzzy logic intermediate values
    attendance_fuzzy_low = db.Column(db.Numeric(5, 4))
    attendance_fuzzy_medium = db.Column(db.Numeric(5, 4))
    attendance_fuzzy_high = db.Column(db.Numeric(5, 4))
    
    punctuality_fuzzy_low = db.Column(db.Numeric(5, 4))
    punctuality_fuzzy_medium = db.Column(db.Numeric(5, 4))
    punctuality_fuzzy_high = db.Column(db.Numeric(5, 4))
    
    compliance_fuzzy_low = db.Column(db.Numeric(5, 4))
    compliance_fuzzy_medium = db.Column(db.Numeric(5, 4))
    compliance_fuzzy_high = db.Column(db.Numeric(5, 4))
    
    # Final results
    discipline_score = db.Column(db.Numeric(5, 2), nullable=False)
    discipline_level = db.Column(db.Enum('Kurang', 'Cukup', 'Disiplin', 'Sangat Disiplin', name='discipline_levels'), nullable=False)
    confidence_level = db.Column(db.Numeric(5, 2))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """Convert evaluation result object to dictionary"""
        return {
            'id': self.id,
            'evaluation_id': self.evaluation_id,
            'fuzzy_values': {
                'attendance': {
                    'low': float(self.attendance_fuzzy_low) if self.attendance_fuzzy_low else None,
                    'medium': float(self.attendance_fuzzy_medium) if self.attendance_fuzzy_medium else None,
                    'high': float(self.attendance_fuzzy_high) if self.attendance_fuzzy_high else None
                },
                'punctuality': {
                    'low': float(self.punctuality_fuzzy_low) if self.punctuality_fuzzy_low else None,
                    'medium': float(self.punctuality_fuzzy_medium) if self.punctuality_fuzzy_medium else None,
                    'high': float(self.punctuality_fuzzy_high) if self.punctuality_fuzzy_high else None
                },
                'compliance': {
                    'low': float(self.compliance_fuzzy_low) if self.compliance_fuzzy_low else None,
                    'medium': float(self.compliance_fuzzy_medium) if self.compliance_fuzzy_medium else None,
                    'high': float(self.compliance_fuzzy_high) if self.compliance_fuzzy_high else None
                }
            },
            'discipline_score': float(self.discipline_score) if self.discipline_score else None,
            'discipline_level': self.discipline_level,
            'confidence_level': float(self.confidence_level) if self.confidence_level else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def get_discipline_level_class(self):
        """Get CSS class for discipline level"""
        level_classes = {
            'Sangat Disiplin': 'discipline-sangat-disiplin',
            'Disiplin': 'discipline-disiplin',
            'Cukup': 'discipline-cukup',
            'Kurang': 'discipline-kurang'
        }
        return level_classes.get(self.discipline_level, 'discipline-kurang')
    
    def get_discipline_level_color(self):
        """Get color for discipline level"""
        level_colors = {
            'Sangat Disiplin': '#28a745',
            'Disiplin': '#17a2b8',
            'Cukup': '#ffc107',
            'Kurang': '#dc3545'
        }
        return level_colors.get(self.discipline_level, '#dc3545')
    
    def __repr__(self):
        return f'<EvaluationResult {self.id}: {self.discipline_level} ({self.discipline_score})>'
