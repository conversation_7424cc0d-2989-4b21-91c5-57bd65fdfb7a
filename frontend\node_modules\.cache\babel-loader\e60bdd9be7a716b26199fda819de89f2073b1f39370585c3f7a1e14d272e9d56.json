{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionItemContext from './AccordionItemContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  eventKey,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-item');\n  const contextValue = useMemo(() => ({\n    eventKey\n  }), [eventKey]);\n  return /*#__PURE__*/_jsx(AccordionItemContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix)\n    })\n  });\n});\nAccordionItem.displayName = 'AccordionItem';\nexport default AccordionItem;", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "useBootstrapPrefix", "AccordionItemContext", "jsx", "_jsx", "AccordionItem", "forwardRef", "as", "Component", "bsPrefix", "className", "eventKey", "props", "ref", "contextValue", "Provider", "value", "children", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/AccordionItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionItemContext from './AccordionItemContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst AccordionItem = /*#__PURE__*/React.forwardRef(({\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  bsPrefix,\n  className,\n  eventKey,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'accordion-item');\n  const contextValue = useMemo(() => ({\n    eventKey\n  }), [eventKey]);\n  return /*#__PURE__*/_jsx(AccordionItemContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, bsPrefix)\n    })\n  });\n});\nAccordionItem.displayName = 'AccordionItem';\nexport default AccordionItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EACnD;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACR,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGR,kBAAkB,CAACQ,QAAQ,EAAE,gBAAgB,CAAC;EACzD,MAAMK,YAAY,GAAGd,OAAO,CAAC,OAAO;IAClCW;EACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACf,OAAO,aAAaP,IAAI,CAACF,oBAAoB,CAACa,QAAQ,EAAE;IACtDC,KAAK,EAAEF,YAAY;IACnBG,QAAQ,EAAE,aAAab,IAAI,CAACI,SAAS,EAAE;MACrCK,GAAG,EAAEA,GAAG;MACR,GAAGD,KAAK;MACRF,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAED,QAAQ;IAC3C,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,aAAa,CAACa,WAAW,GAAG,eAAe;AAC3C,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}