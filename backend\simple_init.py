#!/usr/bin/env python3
"""
Simple database initialization script
"""

import os
import sys
from datetime import datetime, date
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
import bcrypt

# Initialize Flask app
app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///fuzzy_discipline.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db = SQLAlchemy(app)

# Define models directly here to avoid import issues
class User(db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), default='hrd')
    full_name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)
    
    def set_password(self, password):
        password_bytes = password.encode('utf-8')
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password_bytes, salt).decode('utf-8')

class Employee(db.Model):
    __tablename__ = 'employees'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(50), nullable=False)
    position = db.Column(db.String(100), nullable=False)
    hire_date = db.Column(db.Date, nullable=False)
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now)
    is_active = db.Column(db.Boolean, default=True)

class Evaluation(db.Model):
    __tablename__ = 'evaluations'
    
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employees.id'), nullable=False)
    evaluator_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    evaluation_period_start = db.Column(db.Date, nullable=False)
    evaluation_period_end = db.Column(db.Date, nullable=False)
    
    # Input data
    total_work_days = db.Column(db.Integer, nullable=False)
    present_days = db.Column(db.Integer, nullable=False)
    late_arrivals = db.Column(db.Integer, nullable=False)
    violations_count = db.Column(db.Integer, nullable=False)
    
    # Calculated scores
    attendance_score = db.Column(db.Numeric(5, 2), nullable=False)
    punctuality_score = db.Column(db.Numeric(5, 2), nullable=False)
    compliance_score = db.Column(db.Numeric(5, 2), nullable=False)
    
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now)

class EvaluationResult(db.Model):
    __tablename__ = 'evaluation_results'
    
    id = db.Column(db.Integer, primary_key=True)
    evaluation_id = db.Column(db.Integer, db.ForeignKey('evaluations.id'), nullable=False)
    
    # Fuzzy membership values
    attendance_fuzzy_low = db.Column(db.Numeric(5, 4))
    attendance_fuzzy_medium = db.Column(db.Numeric(5, 4))
    attendance_fuzzy_high = db.Column(db.Numeric(5, 4))
    
    punctuality_fuzzy_low = db.Column(db.Numeric(5, 4))
    punctuality_fuzzy_medium = db.Column(db.Numeric(5, 4))
    punctuality_fuzzy_high = db.Column(db.Numeric(5, 4))
    
    compliance_fuzzy_low = db.Column(db.Numeric(5, 4))
    compliance_fuzzy_medium = db.Column(db.Numeric(5, 4))
    compliance_fuzzy_high = db.Column(db.Numeric(5, 4))
    
    # Final results
    discipline_score = db.Column(db.Numeric(5, 2), nullable=False)
    discipline_level = db.Column(db.String(20), nullable=False)
    confidence_level = db.Column(db.Numeric(5, 2))
    
    created_at = db.Column(db.DateTime, default=datetime.now)

def init_database():
    """Initialize database with tables and sample data"""
    print("Creating database tables...")
    
    with app.app_context():
        # Drop all tables and recreate
        db.drop_all()
        db.create_all()
        
        print("Tables created successfully!")
        
        # Create default admin user
        print("Creating default admin user...")
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            full_name='System Administrator'
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        
        # Create HRD user
        hrd_user = User(
            username='hrd',
            email='<EMAIL>',
            role='hrd',
            full_name='HR Department'
        )
        hrd_user.set_password('hrd123')
        db.session.add(hrd_user)
        
        # Create sample employees
        print("Creating sample employees...")
        employees_data = [
            {
                'employee_id': 'EMP001',
                'full_name': 'Ahmad Rizki Pratama',
                'department': 'IT',
                'position': 'Software Developer',
                'hire_date': date(2022, 1, 15),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP002',
                'full_name': 'Siti Nurhaliza Putri',
                'department': 'Finance',
                'position': 'Accountant',
                'hire_date': date(2021, 6, 10),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP003',
                'full_name': 'Budi Santoso Wijaya',
                'department': 'Marketing',
                'position': 'Marketing Manager',
                'hire_date': date(2020, 3, 20),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP004',
                'full_name': 'Maya Sari Dewi',
                'department': 'HR',
                'position': 'HR Specialist',
                'hire_date': date(2023, 2, 1),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP005',
                'full_name': 'Andi Wijaya Kusuma',
                'department': 'Operations',
                'position': 'Operations Supervisor',
                'hire_date': date(2019, 11, 5),
                'email': '<EMAIL>',
                'phone': '081234567894'
            },
            {
                'employee_id': 'EMP006',
                'full_name': 'Dewi Lestari Sari',
                'department': 'IT',
                'position': 'UI/UX Designer',
                'hire_date': date(2022, 9, 18),
                'email': '<EMAIL>',
                'phone': '081234567895'
            },
            {
                'employee_id': 'EMP007',
                'full_name': 'Rudi Hartono',
                'department': 'Sales',
                'position': 'Sales Representative',
                'hire_date': date(2021, 11, 25),
                'email': '<EMAIL>',
                'phone': '081234567896'
            },
            {
                'employee_id': 'EMP008',
                'full_name': 'Indira Maharani',
                'department': 'Finance',
                'position': 'Financial Analyst',
                'hire_date': date(2023, 4, 8),
                'email': '<EMAIL>',
                'phone': '081234567897'
            },
            {
                'employee_id': 'EMP009',
                'full_name': 'Fajar Nugroho',
                'department': 'IT',
                'position': 'System Administrator',
                'hire_date': date(2020, 7, 14),
                'email': '<EMAIL>',
                'phone': '081234567898'
            },
            {
                'employee_id': 'EMP010',
                'full_name': 'Rina Anggraini',
                'department': 'HR',
                'position': 'Recruitment Specialist',
                'hire_date': date(2022, 12, 3),
                'email': '<EMAIL>',
                'phone': '081234567899'
            }
        ]
        
        for emp_data in employees_data:
            employee = Employee(**emp_data)
            db.session.add(employee)
        
        # Commit all changes
        db.session.commit()
        
        print("✅ Database initialized successfully!")
        print("\nDefault login credentials:")
        print("  Admin: admin / admin123")
        print("  HRD: hrd / hrd123")
        print("\nSample employees created:")
        for emp in employees_data:
            print(f"  - {emp['employee_id']}: {emp['full_name']} ({emp['department']})")

if __name__ == '__main__':
    init_database()
