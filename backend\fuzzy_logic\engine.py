import numpy as np
import skfuzzy as fuzz
from skfuzzy import control as ctrl
import matplotlib.pyplot as plt
import io
import base64

class FuzzyLogicEngine:
    """
    Fuzzy Logic Engine for Employee Discipline Evaluation
    
    This engine processes three input criteria:
    - Attendance (Kehadiran): 0-100%
    - Punctuality (Ketepatan Waktu): 0-100%
    - Compliance (Kepatuhan): 0-100%
    
    And produces a discipline score and level:
    - Score: 0-100
    - Level: Kurang, Cukup, Di<PERSON>lin, Sangat Disiplin
    """
    
    def __init__(self):
        self.setup_fuzzy_system()
    
    def setup_fuzzy_system(self):
        """Initialize the fuzzy logic system with membership functions and rules"""
        
        # Define input variables
        self.attendance = ctrl.Antecedent(np.arange(0, 101, 1), 'attendance')
        self.punctuality = ctrl.Antecedent(np.arange(0, 101, 1), 'punctuality')
        self.compliance = ctrl.Antecedent(np.arange(0, 101, 1), 'compliance')
        
        # Define output variable
        self.discipline = ctrl.Consequent(np.arange(0, 101, 1), 'discipline')
        
        # Define membership functions for inputs
        # Attendance membership functions
        self.attendance['low'] = fuzz.trimf(self.attendance.universe, [0, 0, 50])
        self.attendance['medium'] = fuzz.trimf(self.attendance.universe, [30, 60, 80])
        self.attendance['high'] = fuzz.trimf(self.attendance.universe, [70, 100, 100])
        
        # Punctuality membership functions
        self.punctuality['low'] = fuzz.trimf(self.punctuality.universe, [0, 0, 50])
        self.punctuality['medium'] = fuzz.trimf(self.punctuality.universe, [30, 60, 80])
        self.punctuality['high'] = fuzz.trimf(self.punctuality.universe, [70, 100, 100])
        
        # Compliance membership functions
        self.compliance['low'] = fuzz.trimf(self.compliance.universe, [0, 0, 50])
        self.compliance['medium'] = fuzz.trimf(self.compliance.universe, [30, 60, 80])
        self.compliance['high'] = fuzz.trimf(self.compliance.universe, [70, 100, 100])
        
        # Define membership functions for output (discipline level)
        self.discipline['kurang'] = fuzz.trimf(self.discipline.universe, [0, 0, 40])
        self.discipline['cukup'] = fuzz.trimf(self.discipline.universe, [20, 50, 70])
        self.discipline['disiplin'] = fuzz.trimf(self.discipline.universe, [60, 80, 90])
        self.discipline['sangat_disiplin'] = fuzz.trimf(self.discipline.universe, [80, 100, 100])
        
        # Define fuzzy rules
        self.rules = [
            # Rule 1: All high -> Sangat Disiplin
            ctrl.Rule(self.attendance['high'] & self.punctuality['high'] & self.compliance['high'], 
                     self.discipline['sangat_disiplin']),
            
            # Rule 2: Two high, one medium -> Disiplin
            ctrl.Rule(self.attendance['high'] & self.punctuality['high'] & self.compliance['medium'], 
                     self.discipline['disiplin']),
            ctrl.Rule(self.attendance['high'] & self.punctuality['medium'] & self.compliance['high'], 
                     self.discipline['disiplin']),
            ctrl.Rule(self.attendance['medium'] & self.punctuality['high'] & self.compliance['high'], 
                     self.discipline['disiplin']),
            
            # Rule 3: Two high, one low -> Cukup
            ctrl.Rule(self.attendance['high'] & self.punctuality['high'] & self.compliance['low'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['high'] & self.punctuality['low'] & self.compliance['high'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['low'] & self.punctuality['high'] & self.compliance['high'], 
                     self.discipline['cukup']),
            
            # Rule 4: All medium -> Disiplin
            ctrl.Rule(self.attendance['medium'] & self.punctuality['medium'] & self.compliance['medium'], 
                     self.discipline['disiplin']),
            
            # Rule 5: Two medium, one high -> Disiplin
            ctrl.Rule(self.attendance['medium'] & self.punctuality['medium'] & self.compliance['high'], 
                     self.discipline['disiplin']),
            ctrl.Rule(self.attendance['medium'] & self.punctuality['high'] & self.compliance['medium'], 
                     self.discipline['disiplin']),
            ctrl.Rule(self.attendance['high'] & self.punctuality['medium'] & self.compliance['medium'], 
                     self.discipline['disiplin']),
            
            # Rule 6: Two medium, one low -> Cukup
            ctrl.Rule(self.attendance['medium'] & self.punctuality['medium'] & self.compliance['low'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['medium'] & self.punctuality['low'] & self.compliance['medium'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['low'] & self.punctuality['medium'] & self.compliance['medium'], 
                     self.discipline['cukup']),
            
            # Rule 7: One high, two medium -> Disiplin
            ctrl.Rule(self.attendance['high'] & self.punctuality['medium'] & self.compliance['medium'], 
                     self.discipline['disiplin']),
            
            # Rule 8: One high, one medium, one low -> Cukup
            ctrl.Rule(self.attendance['high'] & self.punctuality['medium'] & self.compliance['low'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['high'] & self.punctuality['low'] & self.compliance['medium'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['medium'] & self.punctuality['high'] & self.compliance['low'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['medium'] & self.punctuality['low'] & self.compliance['high'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['low'] & self.punctuality['high'] & self.compliance['medium'], 
                     self.discipline['cukup']),
            ctrl.Rule(self.attendance['low'] & self.punctuality['medium'] & self.compliance['high'], 
                     self.discipline['cukup']),
            
            # Rule 9: Two low, one high -> Kurang
            ctrl.Rule(self.attendance['low'] & self.punctuality['low'] & self.compliance['high'], 
                     self.discipline['kurang']),
            ctrl.Rule(self.attendance['low'] & self.punctuality['high'] & self.compliance['low'], 
                     self.discipline['kurang']),
            ctrl.Rule(self.attendance['high'] & self.punctuality['low'] & self.compliance['low'], 
                     self.discipline['kurang']),
            
            # Rule 10: Two low, one medium -> Kurang
            ctrl.Rule(self.attendance['low'] & self.punctuality['low'] & self.compliance['medium'], 
                     self.discipline['kurang']),
            ctrl.Rule(self.attendance['low'] & self.punctuality['medium'] & self.compliance['low'], 
                     self.discipline['kurang']),
            ctrl.Rule(self.attendance['medium'] & self.punctuality['low'] & self.compliance['low'], 
                     self.discipline['kurang']),
            
            # Rule 11: All low -> Kurang
            ctrl.Rule(self.attendance['low'] & self.punctuality['low'] & self.compliance['low'], 
                     self.discipline['kurang']),
        ]
        
        # Create control system
        self.discipline_ctrl = ctrl.ControlSystem(self.rules)
        self.discipline_sim = ctrl.ControlSystemSimulation(self.discipline_ctrl)
    
    def evaluate(self, attendance, punctuality, compliance):
        """
        Evaluate employee discipline using fuzzy logic
        
        Args:
            attendance (float): Attendance score (0-100)
            punctuality (float): Punctuality score (0-100)
            compliance (float): Compliance score (0-100)
        
        Returns:
            dict: Evaluation results including fuzzy values, crisp score, and discipline level
        """
        
        # Set input values
        self.discipline_sim.input['attendance'] = attendance
        self.discipline_sim.input['punctuality'] = punctuality
        self.discipline_sim.input['compliance'] = compliance
        
        # Compute the result
        self.discipline_sim.compute()
        
        # Get crisp output
        discipline_score = self.discipline_sim.output['discipline']
        
        # Calculate membership values for inputs
        attendance_levels = {
            'low': fuzz.interp_membership(self.attendance.universe, self.attendance['low'].mf, attendance),
            'medium': fuzz.interp_membership(self.attendance.universe, self.attendance['medium'].mf, attendance),
            'high': fuzz.interp_membership(self.attendance.universe, self.attendance['high'].mf, attendance)
        }
        
        punctuality_levels = {
            'low': fuzz.interp_membership(self.punctuality.universe, self.punctuality['low'].mf, punctuality),
            'medium': fuzz.interp_membership(self.punctuality.universe, self.punctuality['medium'].mf, punctuality),
            'high': fuzz.interp_membership(self.punctuality.universe, self.punctuality['high'].mf, punctuality)
        }
        
        compliance_levels = {
            'low': fuzz.interp_membership(self.compliance.universe, self.compliance['low'].mf, compliance),
            'medium': fuzz.interp_membership(self.compliance.universe, self.compliance['medium'].mf, compliance),
            'high': fuzz.interp_membership(self.compliance.universe, self.compliance['high'].mf, compliance)
        }
        
        # Determine discipline level based on score
        discipline_level = self._get_discipline_level(discipline_score)
        
        # Calculate confidence level (based on the strength of the dominant membership)
        confidence_level = self._calculate_confidence(attendance_levels, punctuality_levels, compliance_levels)
        
        return {
            'discipline_score': round(discipline_score, 2),
            'discipline_level': discipline_level,
            'confidence_level': round(confidence_level, 2),
            'fuzzy_values': {
                'attendance': attendance_levels,
                'punctuality': punctuality_levels,
                'compliance': compliance_levels
            },
            'input_values': {
                'attendance': attendance,
                'punctuality': punctuality,
                'compliance': compliance
            }
        }
    
    def _get_discipline_level(self, score):
        """Convert numeric score to discipline level"""
        if score >= 80:
            return 'Sangat Disiplin'
        elif score >= 60:
            return 'Disiplin'
        elif score >= 40:
            return 'Cukup'
        else:
            return 'Kurang'
    
    def _calculate_confidence(self, attendance_levels, punctuality_levels, compliance_levels):
        """Calculate confidence level based on membership strengths"""
        # Get the maximum membership value for each input
        max_attendance = max(attendance_levels.values())
        max_punctuality = max(punctuality_levels.values())
        max_compliance = max(compliance_levels.values())
        
        # Average of maximum memberships as confidence
        confidence = (max_attendance + max_punctuality + max_compliance) / 3 * 100
        return confidence
    
    def visualize_membership_functions(self):
        """Generate visualization of membership functions"""
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Attendance
        axes[0, 0].plot(self.attendance.universe, self.attendance['low'].mf, 'b', linewidth=1.5, label='Low')
        axes[0, 0].plot(self.attendance.universe, self.attendance['medium'].mf, 'g', linewidth=1.5, label='Medium')
        axes[0, 0].plot(self.attendance.universe, self.attendance['high'].mf, 'r', linewidth=1.5, label='High')
        axes[0, 0].set_title('Attendance Membership Functions')
        axes[0, 0].legend()
        
        # Punctuality
        axes[0, 1].plot(self.punctuality.universe, self.punctuality['low'].mf, 'b', linewidth=1.5, label='Low')
        axes[0, 1].plot(self.punctuality.universe, self.punctuality['medium'].mf, 'g', linewidth=1.5, label='Medium')
        axes[0, 1].plot(self.punctuality.universe, self.punctuality['high'].mf, 'r', linewidth=1.5, label='High')
        axes[0, 1].set_title('Punctuality Membership Functions')
        axes[0, 1].legend()
        
        # Compliance
        axes[1, 0].plot(self.compliance.universe, self.compliance['low'].mf, 'b', linewidth=1.5, label='Low')
        axes[1, 0].plot(self.compliance.universe, self.compliance['medium'].mf, 'g', linewidth=1.5, label='Medium')
        axes[1, 0].plot(self.compliance.universe, self.compliance['high'].mf, 'r', linewidth=1.5, label='High')
        axes[1, 0].set_title('Compliance Membership Functions')
        axes[1, 0].legend()
        
        # Discipline Output
        axes[1, 1].plot(self.discipline.universe, self.discipline['kurang'].mf, 'r', linewidth=1.5, label='Kurang')
        axes[1, 1].plot(self.discipline.universe, self.discipline['cukup'].mf, 'orange', linewidth=1.5, label='Cukup')
        axes[1, 1].plot(self.discipline.universe, self.discipline['disiplin'].mf, 'b', linewidth=1.5, label='Disiplin')
        axes[1, 1].plot(self.discipline.universe, self.discipline['sangat_disiplin'].mf, 'g', linewidth=1.5, label='Sangat Disiplin')
        axes[1, 1].set_title('Discipline Output Membership Functions')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        # Convert plot to base64 string
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
        img_buffer.seek(0)
        img_string = base64.b64encode(img_buffer.read()).decode()
        plt.close()
        
        return img_string
