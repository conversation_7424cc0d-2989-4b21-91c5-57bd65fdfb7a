# Deployment Guide

## Overview

This guide covers deploying the Fuzzy Logic Employee Discipline Evaluation System to production environments.

## Deployment Options

### 1. Traditional Server Deployment
### 2. Docker Containerization
### 3. Cloud Platform Deployment
### 4. Kubernetes Orchestration

## Prerequisites

### System Requirements

- **OS**: Linux (Ubuntu 20.04+ recommended), Windows Server, or macOS
- **RAM**: Minimum 2GB, Recommended 4GB+
- **Storage**: Minimum 10GB free space
- **Network**: Stable internet connection for dependencies

### Software Requirements

- **Python**: 3.8 or higher
- **Node.js**: 16 or higher
- **Database**: MySQL 8.0+ or PostgreSQL 12+
- **Web Server**: Nginx or Apache (recommended)
- **Process Manager**: PM2, Supervisor, or systemd

## Traditional Server Deployment

### Step 1: Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3 python3-pip python3-venv nodejs npm nginx mysql-server

# Install PM2 for process management
sudo npm install -g pm2
```

### Step 2: Database Setup

```bash
# MySQL setup
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE fuzzy_discipline_db;
CREATE USER 'fuzzy_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON fuzzy_discipline_db.* TO 'fuzzy_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### Step 3: Application Deployment

```bash
# Create application directory
sudo mkdir -p /var/www/fuzzy-discipline
sudo chown $USER:$USER /var/www/fuzzy-discipline
cd /var/www/fuzzy-discipline

# Clone or upload application files
git clone <repository-url> .
# OR upload files via SCP/SFTP

# Backend setup
cd backend
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Configure environment
cp .env.example .env
nano .env
```

Edit `.env` file:
```env
DATABASE_URL=mysql+pymysql://fuzzy_user:secure_password@localhost/fuzzy_discipline_db
SECRET_KEY=your-production-secret-key
JWT_SECRET_KEY=your-production-jwt-key
DEBUG=False
```

```bash
# Initialize database
python init_db.py

# Test backend
python app.py
```

### Step 4: Frontend Build

```bash
cd ../frontend
npm install
npm run build

# Copy build files to web server directory
sudo cp -r build/* /var/www/html/
```

### Step 5: Process Management

Create PM2 ecosystem file:

```bash
# /var/www/fuzzy-discipline/ecosystem.config.js
module.exports = {
  apps: [{
    name: 'fuzzy-backend',
    cwd: '/var/www/fuzzy-discipline/backend',
    script: 'venv/bin/python',
    args: 'app.py',
    env: {
      NODE_ENV: 'production'
    },
    instances: 2,
    exec_mode: 'fork',
    watch: false,
    max_memory_restart: '1G',
    error_file: '/var/log/fuzzy-backend-error.log',
    out_file: '/var/log/fuzzy-backend-out.log',
    log_file: '/var/log/fuzzy-backend.log'
  }]
};
```

Start the application:
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Step 6: Nginx Configuration

```bash
sudo nano /etc/nginx/sites-available/fuzzy-discipline
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # Frontend
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
    }
    
    # Backend API
    location /api {
        proxy_pass http://127.0.0.1:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/fuzzy-discipline /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## Docker Deployment

### Backend Dockerfile

```dockerfile
# backend/Dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 5000

CMD ["python", "app.py"]
```

### Frontend Dockerfile

```dockerfile
# frontend/Dockerfile
FROM node:16-alpine as build

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  database:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: fuzzy_discipline_db
      MYSQL_USER: fuzzy_user
      MYSQL_PASSWORD: fuzzy_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "3306:3306"
    restart: unless-stopped

  backend:
    build: ./backend
    environment:
      DATABASE_URL: mysql+pymysql://fuzzy_user:fuzzy_password@database/fuzzy_discipline_db
      SECRET_KEY: production-secret-key
      JWT_SECRET_KEY: production-jwt-key
      DEBUG: "False"
    depends_on:
      - database
    ports:
      - "5000:5000"
    restart: unless-stopped
    volumes:
      - ./backend/reports:/app/reports

  frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  mysql_data:
```

Deploy with Docker Compose:
```bash
docker-compose up -d
```

## Cloud Platform Deployment

### AWS Deployment

#### Using AWS Elastic Beanstalk

1. **Prepare Application**
   ```bash
   # Create deployment package
   zip -r fuzzy-discipline.zip . -x "*.git*" "node_modules/*" "venv/*"
   ```

2. **Deploy to Elastic Beanstalk**
   - Create new application
   - Upload deployment package
   - Configure environment variables
   - Set up RDS database

#### Using AWS ECS

1. **Build and Push Docker Images**
   ```bash
   # Build images
   docker build -t fuzzy-backend ./backend
   docker build -t fuzzy-frontend ./frontend
   
   # Tag for ECR
   docker tag fuzzy-backend:latest 123456789012.dkr.ecr.region.amazonaws.com/fuzzy-backend:latest
   docker tag fuzzy-frontend:latest 123456789012.dkr.ecr.region.amazonaws.com/fuzzy-frontend:latest
   
   # Push to ECR
   docker push 123456789012.dkr.ecr.region.amazonaws.com/fuzzy-backend:latest
   docker push 123456789012.dkr.ecr.region.amazonaws.com/fuzzy-frontend:latest
   ```

2. **Create ECS Task Definition**
3. **Set up Application Load Balancer**
4. **Configure Auto Scaling**

### Google Cloud Platform

#### Using Google App Engine

1. **Backend app.yaml**
   ```yaml
   runtime: python39
   
   env_variables:
     DATABASE_URL: mysql+pymysql://user:pass@/dbname?unix_socket=/cloudsql/project:region:instance
     SECRET_KEY: your-secret-key
     JWT_SECRET_KEY: your-jwt-key
   
   automatic_scaling:
     min_instances: 1
     max_instances: 10
   ```

2. **Deploy**
   ```bash
   gcloud app deploy backend/app.yaml
   gcloud app deploy frontend/app.yaml
   ```

### Microsoft Azure

#### Using Azure App Service

1. **Create Resource Group**
2. **Deploy Backend to App Service**
3. **Deploy Frontend to Static Web Apps**
4. **Configure Azure Database for MySQL**

## Kubernetes Deployment

### Backend Deployment

```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fuzzy-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fuzzy-backend
  template:
    metadata:
      labels:
        app: fuzzy-backend
    spec:
      containers:
      - name: backend
        image: fuzzy-backend:latest
        ports:
        - containerPort: 5000
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: database-url
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: app-secret
              key: secret-key
---
apiVersion: v1
kind: Service
metadata:
  name: fuzzy-backend-service
spec:
  selector:
    app: fuzzy-backend
  ports:
  - port: 80
    targetPort: 5000
  type: ClusterIP
```

### Frontend Deployment

```yaml
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fuzzy-frontend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: fuzzy-frontend
  template:
    metadata:
      labels:
        app: fuzzy-frontend
    spec:
      containers:
      - name: frontend
        image: fuzzy-frontend:latest
        ports:
        - containerPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: fuzzy-frontend-service
spec:
  selector:
    app: fuzzy-frontend
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
```

Deploy to Kubernetes:
```bash
kubectl apply -f k8s/
```

## SSL/TLS Configuration

### Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Manual SSL Certificate

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # ... rest of configuration
}
```

## Monitoring and Logging

### Application Monitoring

```bash
# Install monitoring tools
npm install -g pm2-logrotate
pm2 install pm2-server-monit
```

### Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/fuzzy-discipline
```

```
/var/log/fuzzy-*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload all
    endscript
}
```

### Health Checks

```bash
# Create health check script
#!/bin/bash
# /usr/local/bin/health-check.sh

# Check backend
if curl -f http://localhost:5000/api/health > /dev/null 2>&1; then
    echo "Backend: OK"
else
    echo "Backend: FAILED"
    pm2 restart fuzzy-backend
fi

# Check frontend
if curl -f http://localhost > /dev/null 2>&1; then
    echo "Frontend: OK"
else
    echo "Frontend: FAILED"
    sudo systemctl restart nginx
fi
```

## Security Considerations

### Firewall Configuration

```bash
# UFW setup
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### Database Security

```sql
-- Remove test databases
DROP DATABASE IF EXISTS test;

-- Secure MySQL installation
DELETE FROM mysql.user WHERE User='';
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');
FLUSH PRIVILEGES;
```

### Application Security

1. **Environment Variables**: Never commit secrets to version control
2. **HTTPS Only**: Force SSL/TLS for all connections
3. **Security Headers**: Implement proper security headers
4. **Input Validation**: Validate all user inputs
5. **Rate Limiting**: Implement API rate limiting
6. **Regular Updates**: Keep dependencies updated

## Backup and Recovery

### Database Backup

```bash
#!/bin/bash
# /usr/local/bin/backup-db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/fuzzy-discipline"
mkdir -p $BACKUP_DIR

mysqldump -u fuzzy_user -p fuzzy_discipline_db > $BACKUP_DIR/db_backup_$DATE.sql
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Keep only last 30 days
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
```

### Application Backup

```bash
#!/bin/bash
# /usr/local/bin/backup-app.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/fuzzy-discipline"
APP_DIR="/var/www/fuzzy-discipline"

tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C $APP_DIR .
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete
```

## Performance Optimization

### Database Optimization

```sql
-- Add indexes for better performance
CREATE INDEX idx_evaluations_employee_id ON evaluations(employee_id);
CREATE INDEX idx_evaluations_created_at ON evaluations(created_at);
CREATE INDEX idx_employees_department ON employees(department);
```

### Application Optimization

1. **Caching**: Implement Redis for session and data caching
2. **CDN**: Use CDN for static assets
3. **Compression**: Enable gzip compression
4. **Database Connection Pooling**: Configure connection pooling
5. **Load Balancing**: Use multiple backend instances

## Troubleshooting

### Common Issues

1. **Database Connection**: Check credentials and network connectivity
2. **Permission Errors**: Verify file permissions and ownership
3. **Port Conflicts**: Ensure ports are not in use by other services
4. **Memory Issues**: Monitor memory usage and adjust limits
5. **SSL Certificate**: Verify certificate validity and configuration

### Debug Commands

```bash
# Check application status
pm2 status
pm2 logs fuzzy-backend

# Check nginx status
sudo nginx -t
sudo systemctl status nginx

# Check database connection
mysql -u fuzzy_user -p -h localhost fuzzy_discipline_db

# Monitor system resources
htop
df -h
free -m
```

This deployment guide provides comprehensive instructions for deploying the Fuzzy Logic Employee Discipline Evaluation System in various environments. Choose the deployment method that best fits your infrastructure and requirements.
