{"ast": null, "code": "\"use client\";\n\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport DropdownContext from '@restart/ui/DropdownContext';\nimport { useDropdownToggle } from '@restart/ui/DropdownToggle';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport Button from './Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport useWrappedRefWithWarning from './useWrappedRefWithWarning';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownToggle = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  split,\n  className,\n  childBsPrefix,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = Button,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-toggle');\n  const dropdownContext = useContext(DropdownContext);\n  if (childBsPrefix !== undefined) {\n    props.bsPrefix = childBsPrefix;\n  }\n  const [toggleProps] = useDropdownToggle();\n  toggleProps.ref = useMergedRefs(toggleProps.ref, useWrappedRefWithWarning(ref, 'DropdownToggle'));\n\n  // This intentionally forwards size and variant (if set) to the\n  // underlying component, to allow it to render size and style variants.\n  return /*#__PURE__*/_jsx(Component, {\n    className: classNames(className, prefix, split && `${prefix}-split`, (dropdownContext == null ? void 0 : dropdownContext.show) && 'show'),\n    ...toggleProps,\n    ...props\n  });\n});\nDropdownToggle.displayName = 'DropdownToggle';\nexport default DropdownToggle;", "map": {"version": 3, "names": ["useMergedRefs", "DropdownContext", "useDropdownToggle", "classNames", "React", "useContext", "<PERSON><PERSON>", "useBootstrapPrefix", "useWrappedRefWithWarning", "jsx", "_jsx", "DropdownToggle", "forwardRef", "bsPrefix", "split", "className", "childBsPrefix", "as", "Component", "props", "ref", "prefix", "dropdownContext", "undefined", "toggleProps", "show", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/DropdownToggle.js"], "sourcesContent": ["\"use client\";\n\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nimport DropdownContext from '@restart/ui/DropdownContext';\nimport { useDropdownToggle } from '@restart/ui/DropdownToggle';\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport Button from './Button';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport useWrappedRefWithWarning from './useWrappedRefWithWarning';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownToggle = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  split,\n  className,\n  childBsPrefix,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = Button,\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'dropdown-toggle');\n  const dropdownContext = useContext(DropdownContext);\n  if (childBsPrefix !== undefined) {\n    props.bsPrefix = childBsPrefix;\n  }\n  const [toggleProps] = useDropdownToggle();\n  toggleProps.ref = useMergedRefs(toggleProps.ref, useWrappedRefWithWarning(ref, 'DropdownToggle'));\n\n  // This intentionally forwards size and variant (if set) to the\n  // underlying component, to allow it to render size and style variants.\n  return /*#__PURE__*/_jsx(Component, {\n    className: classNames(className, prefix, split && `${prefix}-split`, (dropdownContext == null ? void 0 : dropdownContext.show) && 'show'),\n    ...toggleProps,\n    ...props\n  });\n});\nDropdownToggle.displayName = 'DropdownToggle';\nexport default DropdownToggle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,8BAA8B;AACxD,OAAOC,eAAe,MAAM,6BAA6B;AACzD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACpDC,QAAQ;EACRC,KAAK;EACLC,SAAS;EACTC,aAAa;EACb;EACAC,EAAE,EAAEC,SAAS,GAAGZ,MAAM;EACtB,GAAGa;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGd,kBAAkB,CAACM,QAAQ,EAAE,iBAAiB,CAAC;EAC9D,MAAMS,eAAe,GAAGjB,UAAU,CAACJ,eAAe,CAAC;EACnD,IAAIe,aAAa,KAAKO,SAAS,EAAE;IAC/BJ,KAAK,CAACN,QAAQ,GAAGG,aAAa;EAChC;EACA,MAAM,CAACQ,WAAW,CAAC,GAAGtB,iBAAiB,CAAC,CAAC;EACzCsB,WAAW,CAACJ,GAAG,GAAGpB,aAAa,CAACwB,WAAW,CAACJ,GAAG,EAAEZ,wBAAwB,CAACY,GAAG,EAAE,gBAAgB,CAAC,CAAC;;EAEjG;EACA;EACA,OAAO,aAAaV,IAAI,CAACQ,SAAS,EAAE;IAClCH,SAAS,EAAEZ,UAAU,CAACY,SAAS,EAAEM,MAAM,EAAEP,KAAK,IAAI,GAAGO,MAAM,QAAQ,EAAE,CAACC,eAAe,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,IAAI,KAAK,MAAM,CAAC;IACzI,GAAGD,WAAW;IACd,GAAGL;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFR,cAAc,CAACe,WAAW,GAAG,gBAAgB;AAC7C,eAAef,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}