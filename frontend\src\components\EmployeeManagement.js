import React, { useState, useEffect } from 'react';
import { 
  Container, Row, Col, Card, Button, Table, Modal, Form, 
  Alert, Spinner, Badge, InputGroup, Pagination 
} from 'react-bootstrap';
import axios from 'axios';
import { toast } from 'react-toastify';

function EmployeeManagement() {
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [formData, setFormData] = useState({
    employee_id: '',
    full_name: '',
    department: '',
    position: '',
    hire_date: '',
    email: '',
    phone: ''
  });
  const [pagination, setPagination] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [departments, setDepartments] = useState([]);

  useEffect(() => {
    fetchEmployees();
    fetchDepartments();
  }, [currentPage, searchTerm, selectedDepartment]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: 10,
        search: searchTerm,
        department: selectedDepartment
      };
      
      const response = await axios.get('/api/employees', { params });
      setEmployees(response.data.employees);
      setPagination(response.data.pagination);
    } catch (err) {
      setError('Gagal memuat data karyawan');
      console.error('Fetch employees error:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchDepartments = async () => {
    try {
      const response = await axios.get('/api/employees/departments');
      setDepartments(response.data.departments);
    } catch (err) {
      console.error('Fetch departments error:', err);
    }
  };

  const handleShowModal = (employee = null) => {
    if (employee) {
      setEditingEmployee(employee);
      setFormData({
        employee_id: employee.employee_id,
        full_name: employee.full_name,
        department: employee.department,
        position: employee.position,
        hire_date: employee.hire_date,
        email: employee.email || '',
        phone: employee.phone || ''
      });
    } else {
      setEditingEmployee(null);
      setFormData({
        employee_id: '',
        full_name: '',
        department: '',
        position: '',
        hire_date: '',
        email: '',
        phone: ''
      });
    }
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingEmployee(null);
    setFormData({
      employee_id: '',
      full_name: '',
      department: '',
      position: '',
      hire_date: '',
      email: '',
      phone: ''
    });
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      if (editingEmployee) {
        await axios.put(`/api/employees/${editingEmployee.id}`, formData);
        toast.success('Karyawan berhasil diperbarui');
      } else {
        await axios.post('/api/employees', formData);
        toast.success('Karyawan berhasil ditambahkan');
      }
      
      handleCloseModal();
      fetchEmployees();
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Terjadi kesalahan';
      toast.error(errorMessage);
    }
  };

  const handleDelete = async (employee) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus karyawan ${employee.full_name}?`)) {
      try {
        await axios.delete(`/api/employees/${employee.id}`);
        toast.success('Karyawan berhasil dihapus');
        fetchEmployees();
      } catch (err) {
        const errorMessage = err.response?.data?.error || 'Gagal menghapus karyawan';
        toast.error(errorMessage);
      }
    }
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  const handleDepartmentFilter = (e) => {
    setSelectedDepartment(e.target.value);
    setCurrentPage(1);
  };

  if (loading && employees.length === 0) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">Manajemen Karyawan</h1>
          <p className="text-muted">Kelola data karyawan perusahaan</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      <Card>
        <Card.Header className="d-flex justify-content-between align-items-center">
          <h5 className="mb-0">Daftar Karyawan</h5>
          <Button variant="primary" onClick={() => handleShowModal()}>
            <i className="bi bi-plus-circle me-2"></i>
            Tambah Karyawan
          </Button>
        </Card.Header>
        
        <Card.Body>
          {/* Filters */}
          <Row className="mb-3">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Cari karyawan..."
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </InputGroup>
            </Col>
            <Col md={3}>
              <Form.Select value={selectedDepartment} onChange={handleDepartmentFilter}>
                <option value="">Semua Departemen</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </Form.Select>
            </Col>
          </Row>

          {/* Employee Table */}
          <Table responsive hover>
            <thead>
              <tr>
                <th>ID Karyawan</th>
                <th>Nama Lengkap</th>
                <th>Departemen</th>
                <th>Posisi</th>
                <th>Tanggal Bergabung</th>
                <th>Status</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              {employees.map((employee) => (
                <tr key={employee.id}>
                  <td><strong>{employee.employee_id}</strong></td>
                  <td>
                    <div>
                      <strong>{employee.full_name}</strong>
                      {employee.email && (
                        <div><small className="text-muted">{employee.email}</small></div>
                      )}
                    </div>
                  </td>
                  <td>{employee.department}</td>
                  <td>{employee.position}</td>
                  <td>{new Date(employee.hire_date).toLocaleDateString('id-ID')}</td>
                  <td>
                    <Badge bg={employee.is_active ? 'success' : 'secondary'}>
                      {employee.is_active ? 'Aktif' : 'Tidak Aktif'}
                    </Badge>
                  </td>
                  <td>
                    <Button
                      variant="outline-primary"
                      size="sm"
                      className="me-2"
                      onClick={() => handleShowModal(employee)}
                    >
                      <i className="bi bi-pencil"></i>
                    </Button>
                    <Button
                      variant="outline-danger"
                      size="sm"
                      onClick={() => handleDelete(employee)}
                    >
                      <i className="bi bi-trash"></i>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>

          {employees.length === 0 && !loading && (
            <div className="text-center py-4">
              <p className="text-muted">Tidak ada data karyawan</p>
            </div>
          )}

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="d-flex justify-content-center mt-3">
              <Pagination>
                <Pagination.Prev 
                  disabled={!pagination.has_prev}
                  onClick={() => handlePageChange(currentPage - 1)}
                />
                {[...Array(pagination.pages)].map((_, index) => (
                  <Pagination.Item
                    key={index + 1}
                    active={index + 1 === currentPage}
                    onClick={() => handlePageChange(index + 1)}
                  >
                    {index + 1}
                  </Pagination.Item>
                ))}
                <Pagination.Next 
                  disabled={!pagination.has_next}
                  onClick={() => handlePageChange(currentPage + 1)}
                />
              </Pagination>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Employee Modal */}
      <Modal show={showModal} onHide={handleCloseModal} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>
            {editingEmployee ? 'Edit Karyawan' : 'Tambah Karyawan'}
          </Modal.Title>
        </Modal.Header>
        <Form onSubmit={handleSubmit}>
          <Modal.Body>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>ID Karyawan *</Form.Label>
                  <Form.Control
                    type="text"
                    name="employee_id"
                    value={formData.employee_id}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Nama Lengkap *</Form.Label>
                  <Form.Control
                    type="text"
                    name="full_name"
                    value={formData.full_name}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Departemen *</Form.Label>
                  <Form.Control
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Posisi *</Form.Label>
                  <Form.Control
                    type="text"
                    name="position"
                    value={formData.position}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
            </Row>
            
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Tanggal Bergabung *</Form.Label>
                  <Form.Control
                    type="date"
                    name="hire_date"
                    value={formData.hire_date}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            
            <Form.Group className="mb-3">
              <Form.Label>Nomor Telepon</Form.Label>
              <Form.Control
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
              />
            </Form.Group>
          </Modal.Body>
          <Modal.Footer>
            <Button variant="secondary" onClick={handleCloseModal}>
              Batal
            </Button>
            <Button variant="primary" type="submit">
              {editingEmployee ? 'Perbarui' : 'Simpan'}
            </Button>
          </Modal.Footer>
        </Form>
      </Modal>
    </Container>
  );
}

export default EmployeeManagement;
