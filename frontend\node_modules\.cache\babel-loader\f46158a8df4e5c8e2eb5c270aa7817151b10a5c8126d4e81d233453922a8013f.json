{"ast": null, "code": "import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Button from './Button';\nimport ButtonGroup from './ButtonGroup';\nimport Dropdown from './Dropdown';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.\n   * @type {string}\n   * @required\n   */\n  id: PropTypes.string,\n  /**\n   * Accessible label for the toggle; the value of `title` if not specified.\n   */\n  toggleLabel: PropTypes.string,\n  /** An `href` passed to the non-toggle Button */\n  href: PropTypes.string,\n  /** An anchor `target` passed to the non-toggle Button */\n  target: PropTypes.string,\n  /** An `onClick` handler passed to the non-toggle Button */\n  onClick: PropTypes.func,\n  /** The content of the non-toggle Button.  */\n  title: PropTypes.node.isRequired,\n  /** A `type` passed to the non-toggle Button */\n  type: PropTypes.string,\n  /** Disables both Buttons  */\n  disabled: PropTypes.bool,\n  /**\n   * Aligns the dropdown menu.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   *\n   * @type {\"start\"|\"end\"|{ sm: \"start\"|\"end\" }|{ md: \"start\"|\"end\" }|{ lg: \"start\"|\"end\" }|{ xl: \"start\"|\"end\"}|{ xxl: \"start\"|\"end\"} }\n   */\n  align: alignPropType,\n  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */\n  menuRole: PropTypes.string,\n  /** Whether to render the dropdown menu in the DOM before the first time it is shown */\n  renderMenuOnMount: PropTypes.bool,\n  /**\n   *  Which event when fired outside the component will cause it to be closed.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   */\n  rootCloseEvent: PropTypes.string,\n  /**\n   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to\n   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).\n   *\n   */\n  flip: PropTypes.bool,\n  /** @ignore */\n  bsPrefix: PropTypes.string,\n  /** @ignore */\n  variant: PropTypes.string,\n  /** @ignore */\n  size: PropTypes.string\n};\n\n/**\n * A convenience component for simple or general use split button dropdowns. Renders a\n * `ButtonGroup` containing a `Button` and a `Button` toggle for the `Dropdown`. All `children`\n * are passed directly to the default `Dropdown.Menu`. This component accepts all of [`Dropdown`'s\n * props](#dropdown-props).\n *\n * _All unknown props are passed through to the `Dropdown` component._\n * The Button `variant`, `size` and `bsPrefix` props are passed to the button and toggle,\n * and menu-related props are passed to the `Dropdown.Menu`\n */\nconst SplitButton = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  size,\n  variant,\n  title,\n  type = 'button',\n  toggleLabel = 'Toggle dropdown',\n  children,\n  onClick,\n  href,\n  target,\n  menuRole,\n  renderMenuOnMount,\n  rootCloseEvent,\n  flip,\n  ...props\n}, ref) => /*#__PURE__*/_jsxs(Dropdown, {\n  ref: ref,\n  ...props,\n  as: ButtonGroup,\n  children: [/*#__PURE__*/_jsx(Button, {\n    size: size,\n    variant: variant,\n    disabled: props.disabled,\n    bsPrefix: bsPrefix,\n    href: href,\n    target: target,\n    onClick: onClick,\n    type: type,\n    children: title\n  }), /*#__PURE__*/_jsx(Dropdown.Toggle, {\n    split: true,\n    id: id,\n    size: size,\n    variant: variant,\n    disabled: props.disabled,\n    childBsPrefix: bsPrefix,\n    children: /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: toggleLabel\n    })\n  }), /*#__PURE__*/_jsx(Dropdown.Menu, {\n    role: menuRole,\n    renderOnMount: renderMenuOnMount,\n    rootCloseEvent: rootCloseEvent,\n    flip: flip,\n    children: children\n  })]\n}));\nSplitButton.propTypes = propTypes;\nSplitButton.displayName = 'SplitButton';\nexport default SplitButton;", "map": {"version": 3, "names": ["React", "PropTypes", "<PERSON><PERSON>", "ButtonGroup", "Dropdown", "alignPropType", "jsx", "_jsx", "jsxs", "_jsxs", "propTypes", "id", "string", "to<PERSON><PERSON><PERSON><PERSON>", "href", "target", "onClick", "func", "title", "node", "isRequired", "type", "disabled", "bool", "align", "menuRole", "renderMenuOnMount", "rootCloseEvent", "flip", "bsPrefix", "variant", "size", "SplitButton", "forwardRef", "children", "props", "ref", "as", "Toggle", "split", "childBsPrefix", "className", "<PERSON><PERSON>", "role", "renderOnMount", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/SplitButton.js"], "sourcesContent": ["import * as React from 'react';\nimport PropTypes from 'prop-types';\nimport Button from './Button';\nimport ButtonGroup from './ButtonGroup';\nimport Dropdown from './Dropdown';\nimport { alignPropType } from './types';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst propTypes = {\n  /**\n   * An html id attribute for the Toggle button, necessary for assistive technologies, such as screen readers.\n   * @type {string}\n   * @required\n   */\n  id: PropTypes.string,\n  /**\n   * Accessible label for the toggle; the value of `title` if not specified.\n   */\n  toggleLabel: PropTypes.string,\n  /** An `href` passed to the non-toggle Button */\n  href: PropTypes.string,\n  /** An anchor `target` passed to the non-toggle Button */\n  target: PropTypes.string,\n  /** An `onClick` handler passed to the non-toggle Button */\n  onClick: PropTypes.func,\n  /** The content of the non-toggle Button.  */\n  title: PropTypes.node.isRequired,\n  /** A `type` passed to the non-toggle Button */\n  type: PropTypes.string,\n  /** Disables both Buttons  */\n  disabled: PropTypes.bool,\n  /**\n   * Aligns the dropdown menu.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   *\n   * @type {\"start\"|\"end\"|{ sm: \"start\"|\"end\" }|{ md: \"start\"|\"end\" }|{ lg: \"start\"|\"end\" }|{ xl: \"start\"|\"end\"}|{ xxl: \"start\"|\"end\"} }\n   */\n  align: alignPropType,\n  /** An ARIA accessible role applied to the Menu component. When set to 'menu', The dropdown */\n  menuRole: PropTypes.string,\n  /** Whether to render the dropdown menu in the DOM before the first time it is shown */\n  renderMenuOnMount: PropTypes.bool,\n  /**\n   *  Which event when fired outside the component will cause it to be closed.\n   *\n   * _see [DropdownMenu](#dropdown-menu-props) for more details_\n   */\n  rootCloseEvent: PropTypes.string,\n  /**\n   * Allow Dropdown to flip in case of an overlapping on the reference element. For more information refer to\n   * Popper.js's flip [docs](https://popper.js.org/docs/v2/modifiers/flip/).\n   *\n   */\n  flip: PropTypes.bool,\n  /** @ignore */\n  bsPrefix: PropTypes.string,\n  /** @ignore */\n  variant: PropTypes.string,\n  /** @ignore */\n  size: PropTypes.string\n};\n\n/**\n * A convenience component for simple or general use split button dropdowns. Renders a\n * `ButtonGroup` containing a `Button` and a `Button` toggle for the `Dropdown`. All `children`\n * are passed directly to the default `Dropdown.Menu`. This component accepts all of [`Dropdown`'s\n * props](#dropdown-props).\n *\n * _All unknown props are passed through to the `Dropdown` component._\n * The Button `variant`, `size` and `bsPrefix` props are passed to the button and toggle,\n * and menu-related props are passed to the `Dropdown.Menu`\n */\nconst SplitButton = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  size,\n  variant,\n  title,\n  type = 'button',\n  toggleLabel = 'Toggle dropdown',\n  children,\n  onClick,\n  href,\n  target,\n  menuRole,\n  renderMenuOnMount,\n  rootCloseEvent,\n  flip,\n  ...props\n}, ref) => /*#__PURE__*/_jsxs(Dropdown, {\n  ref: ref,\n  ...props,\n  as: ButtonGroup,\n  children: [/*#__PURE__*/_jsx(Button, {\n    size: size,\n    variant: variant,\n    disabled: props.disabled,\n    bsPrefix: bsPrefix,\n    href: href,\n    target: target,\n    onClick: onClick,\n    type: type,\n    children: title\n  }), /*#__PURE__*/_jsx(Dropdown.Toggle, {\n    split: true,\n    id: id,\n    size: size,\n    variant: variant,\n    disabled: props.disabled,\n    childBsPrefix: bsPrefix,\n    children: /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: toggleLabel\n    })\n  }), /*#__PURE__*/_jsx(Dropdown.Menu, {\n    role: menuRole,\n    renderOnMount: renderMenuOnMount,\n    rootCloseEvent: rootCloseEvent,\n    flip: flip,\n    children: children\n  })]\n}));\nSplitButton.propTypes = propTypes;\nSplitButton.displayName = 'SplitButton';\nexport default SplitButton;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,aAAa,QAAQ,SAAS;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,SAAS,GAAG;EAChB;AACF;AACA;AACA;AACA;EACEC,EAAE,EAAEV,SAAS,CAACW,MAAM;EACpB;AACF;AACA;EACEC,WAAW,EAAEZ,SAAS,CAACW,MAAM;EAC7B;EACAE,IAAI,EAAEb,SAAS,CAACW,MAAM;EACtB;EACAG,MAAM,EAAEd,SAAS,CAACW,MAAM;EACxB;EACAI,OAAO,EAAEf,SAAS,CAACgB,IAAI;EACvB;EACAC,KAAK,EAAEjB,SAAS,CAACkB,IAAI,CAACC,UAAU;EAChC;EACAC,IAAI,EAAEpB,SAAS,CAACW,MAAM;EACtB;EACAU,QAAQ,EAAErB,SAAS,CAACsB,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAEnB,aAAa;EACpB;EACAoB,QAAQ,EAAExB,SAAS,CAACW,MAAM;EAC1B;EACAc,iBAAiB,EAAEzB,SAAS,CAACsB,IAAI;EACjC;AACF;AACA;AACA;AACA;EACEI,cAAc,EAAE1B,SAAS,CAACW,MAAM;EAChC;AACF;AACA;AACA;AACA;EACEgB,IAAI,EAAE3B,SAAS,CAACsB,IAAI;EACpB;EACAM,QAAQ,EAAE5B,SAAS,CAACW,MAAM;EAC1B;EACAkB,OAAO,EAAE7B,SAAS,CAACW,MAAM;EACzB;EACAmB,IAAI,EAAE9B,SAAS,CAACW;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,WAAW,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,CAAC;EACjDtB,EAAE;EACFkB,QAAQ;EACRE,IAAI;EACJD,OAAO;EACPZ,KAAK;EACLG,IAAI,GAAG,QAAQ;EACfR,WAAW,GAAG,iBAAiB;EAC/BqB,QAAQ;EACRlB,OAAO;EACPF,IAAI;EACJC,MAAM;EACNU,QAAQ;EACRC,iBAAiB;EACjBC,cAAc;EACdC,IAAI;EACJ,GAAGO;AACL,CAAC,EAAEC,GAAG,KAAK,aAAa3B,KAAK,CAACL,QAAQ,EAAE;EACtCgC,GAAG,EAAEA,GAAG;EACR,GAAGD,KAAK;EACRE,EAAE,EAAElC,WAAW;EACf+B,QAAQ,EAAE,CAAC,aAAa3B,IAAI,CAACL,MAAM,EAAE;IACnC6B,IAAI,EAAEA,IAAI;IACVD,OAAO,EAAEA,OAAO;IAChBR,QAAQ,EAAEa,KAAK,CAACb,QAAQ;IACxBO,QAAQ,EAAEA,QAAQ;IAClBf,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,OAAO,EAAEA,OAAO;IAChBK,IAAI,EAAEA,IAAI;IACVa,QAAQ,EAAEhB;EACZ,CAAC,CAAC,EAAE,aAAaX,IAAI,CAACH,QAAQ,CAACkC,MAAM,EAAE;IACrCC,KAAK,EAAE,IAAI;IACX5B,EAAE,EAAEA,EAAE;IACNoB,IAAI,EAAEA,IAAI;IACVD,OAAO,EAAEA,OAAO;IAChBR,QAAQ,EAAEa,KAAK,CAACb,QAAQ;IACxBkB,aAAa,EAAEX,QAAQ;IACvBK,QAAQ,EAAE,aAAa3B,IAAI,CAAC,MAAM,EAAE;MAClCkC,SAAS,EAAE,iBAAiB;MAC5BP,QAAQ,EAAErB;IACZ,CAAC;EACH,CAAC,CAAC,EAAE,aAAaN,IAAI,CAACH,QAAQ,CAACsC,IAAI,EAAE;IACnCC,IAAI,EAAElB,QAAQ;IACdmB,aAAa,EAAElB,iBAAiB;IAChCC,cAAc,EAAEA,cAAc;IAC9BC,IAAI,EAAEA,IAAI;IACVM,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;AACHF,WAAW,CAACtB,SAAS,GAAGA,SAAS;AACjCsB,WAAW,CAACa,WAAW,GAAG,aAAa;AACvC,eAAeb,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}