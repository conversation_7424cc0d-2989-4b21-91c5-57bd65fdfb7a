from flask import Blueprint, request, jsonify, send_file
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user import User
from models.employee import Employee
from models.evaluation import Evaluation
from models.evaluation_result import EvaluationResult
from models.report import Report, db
from datetime import datetime
import os
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('', methods=['GET'])
@jwt_required()
def get_reports():
    """Get all reports with optional filtering"""
    try:
        # Query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        report_type = request.args.get('report_type', '')
        employee_id = request.args.get('employee_id', type=int)
        
        # Build query
        query = Report.query.join(Evaluation).join(Employee)
        
        if report_type:
            query = query.filter(Report.report_type == report_type)
        
        if employee_id:
            query = query.filter(Evaluation.employee_id == employee_id)
        
        # Paginate results
        reports = query.order_by(Report.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'reports': [report.to_dict() for report in reports.items],
            'pagination': {
                'page': reports.page,
                'pages': reports.pages,
                'per_page': reports.per_page,
                'total': reports.total,
                'has_next': reports.has_next,
                'has_prev': reports.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/<int:report_id>', methods=['GET'])
@jwt_required()
def get_report(report_id):
    """Get specific report by ID"""
    try:
        report = Report.query.get(report_id)
        
        if not report:
            return jsonify({'error': 'Report not found'}), 404
        
        return jsonify(report.to_dict()), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/generate', methods=['POST'])
@jwt_required()
def generate_report():
    """Generate PDF report for evaluation"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        data = request.get_json()
        evaluation_id = data.get('evaluation_id')
        report_type = data.get('report_type', 'individual')
        
        if not evaluation_id:
            return jsonify({'error': 'evaluation_id is required'}), 400
        
        evaluation = Evaluation.query.get(evaluation_id)
        if not evaluation:
            return jsonify({'error': 'Evaluation not found'}), 404
        
        if not evaluation.result:
            return jsonify({'error': 'Evaluation result not found'}), 404
        
        # Generate PDF
        reports_dir = os.path.join(os.path.dirname(__file__), '..', 'reports')
        os.makedirs(reports_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'evaluation_report_{evaluation.employee.employee_id}_{timestamp}.pdf'
        filepath = os.path.join(reports_dir, filename)
        
        # Create PDF
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # Center alignment
        )
        story.append(Paragraph("LAPORAN EVALUASI KEDISIPLINAN KARYAWAN", title_style))
        story.append(Spacer(1, 20))
        
        # Employee Information
        story.append(Paragraph("INFORMASI KARYAWAN", styles['Heading2']))
        employee_data = [
            ['ID Karyawan:', evaluation.employee.employee_id],
            ['Nama Lengkap:', evaluation.employee.full_name],
            ['Departemen:', evaluation.employee.department],
            ['Posisi:', evaluation.employee.position],
            ['Tanggal Bergabung:', evaluation.employee.hire_date.strftime('%d/%m/%Y')],
        ]
        
        employee_table = Table(employee_data, colWidths=[2*inch, 4*inch])
        employee_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(employee_table)
        story.append(Spacer(1, 20))
        
        # Evaluation Period
        story.append(Paragraph("PERIODE EVALUASI", styles['Heading2']))
        period_data = [
            ['Tanggal Mulai:', evaluation.evaluation_period_start.strftime('%d/%m/%Y')],
            ['Tanggal Selesai:', evaluation.evaluation_period_end.strftime('%d/%m/%Y')],
            ['Evaluator:', evaluation.evaluator.full_name],
        ]
        
        period_table = Table(period_data, colWidths=[2*inch, 4*inch])
        period_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        story.append(period_table)
        story.append(Spacer(1, 20))
        
        # Evaluation Scores
        story.append(Paragraph("SKOR EVALUASI", styles['Heading2']))
        scores_data = [
            ['Kriteria', 'Skor', 'Keterangan'],
            ['Kehadiran', f'{evaluation.attendance_score}%', f'{evaluation.present_days}/{evaluation.total_work_days} hari'],
            ['Ketepatan Waktu', f'{evaluation.punctuality_score}%', f'{evaluation.late_arrivals} kali terlambat'],
            ['Kepatuhan', f'{evaluation.compliance_score}%', f'{evaluation.violations_count} pelanggaran'],
        ]
        
        scores_table = Table(scores_data, colWidths=[2*inch, 1.5*inch, 2.5*inch])
        scores_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        story.append(scores_table)
        story.append(Spacer(1, 20))
        
        # Fuzzy Logic Results
        story.append(Paragraph("HASIL ANALISIS FUZZY LOGIC", styles['Heading2']))
        result = evaluation.result
        
        # Final Result
        result_data = [
            ['Skor Kedisiplinan:', f'{result.discipline_score}'],
            ['Tingkat Kedisiplinan:', result.discipline_level],
            ['Tingkat Kepercayaan:', f'{result.confidence_level}%' if result.confidence_level else 'N/A'],
        ]
        
        result_table = Table(result_data, colWidths=[2*inch, 4*inch])
        result_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('BACKGROUND', (0, 1), (1, 1), result.get_discipline_level_color()),
            ('TEXTCOLOR', (0, 1), (1, 1), colors.white if result.discipline_level != 'Cukup' else colors.black),
        ]))
        story.append(result_table)
        story.append(Spacer(1, 20))
        
        # Notes
        if evaluation.notes:
            story.append(Paragraph("CATATAN", styles['Heading2']))
            story.append(Paragraph(evaluation.notes, styles['Normal']))
            story.append(Spacer(1, 20))
        
        # Footer
        story.append(Spacer(1, 30))
        footer_text = f"Laporan dibuat pada: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}"
        story.append(Paragraph(footer_text, styles['Normal']))
        
        # Build PDF
        doc.build(story)
        
        # Save report record
        report = Report(
            evaluation_id=evaluation_id,
            generated_by=current_user_id,
            report_type=report_type,
            file_path=filepath,
            file_name=filename
        )
        
        db.session.add(report)
        db.session.commit()
        
        return jsonify({
            'message': 'Report generated successfully',
            'report': report.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/<int:report_id>/download', methods=['GET'])
@jwt_required()
def download_report(report_id):
    """Download report file"""
    try:
        report = Report.query.get(report_id)
        
        if not report:
            return jsonify({'error': 'Report not found'}), 404
        
        if not report.file_path or not os.path.exists(report.file_path):
            return jsonify({'error': 'Report file not found'}), 404
        
        return send_file(
            report.file_path,
            as_attachment=True,
            download_name=report.file_name,
            mimetype='application/pdf'
        )
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@reports_bp.route('/<int:report_id>', methods=['DELETE'])
@jwt_required()
def delete_report(report_id):
    """Delete report"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # Check if user has permission (admin or report owner)
        report = Report.query.get(report_id)
        if not report:
            return jsonify({'error': 'Report not found'}), 404
        
        if user.role != 'admin' and report.generated_by != current_user_id:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        # Delete file if exists
        if report.file_path and os.path.exists(report.file_path):
            os.remove(report.file_path)
        
        db.session.delete(report)
        db.session.commit()
        
        return jsonify({'message': 'Report deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500
