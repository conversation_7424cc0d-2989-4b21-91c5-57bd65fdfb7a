# Fuzzy Logic Employee Discipline Evaluation System - Frontend

React-based web application for employee discipline evaluation using fuzzy logic.

## Features

- **Modern UI**: Bootstrap-based responsive design
- **Authentication**: Secure login with JWT tokens
- **Employee Management**: Add, edit, and manage employee data
- **Evaluation Forms**: Interactive forms with real-time score calculation
- **Results Dashboard**: Visual display of fuzzy logic results
- **Report Generation**: PDF report download functionality
- **Real-time Feedback**: Toast notifications and loading states

## Technology Stack

- **Framework**: React 18.2.0
- **Routing**: React Router DOM 6.15.0
- **UI Components**: React Bootstrap 2.8.0
- **HTTP Client**: Axios 1.5.0
- **Charts**: Chart.js with react-chartjs-2
- **PDF Generation**: jsPDF and html2canvas
- **Notifications**: React Toastify

## Installation

### Prerequisites

- Node.js 16 or higher
- npm or yarn package manager
- Backend API running on port 5000

### Setup Steps

1. **Install Dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

2. **Environment Configuration**
   
   The app is configured to proxy API requests to `http://localhost:5000`. 
   If your backend runs on a different port, update the `proxy` field in `package.json`.

3. **Start Development Server**
   ```bash
   npm start
   # or
   yarn start
   ```

The application will be available at `http://localhost:3000`

## Project Structure

```
src/
├── components/           # React components
│   ├── Dashboard.js     # Main dashboard
│   ├── Login.js         # Authentication
│   ├── Navigation.js    # Navigation bar
│   ├── EmployeeManagement.js  # Employee CRUD
│   ├── EvaluationForm.js      # Evaluation form
│   ├── EvaluationResults.js   # Results display
│   └── Reports.js       # Report management
├── contexts/            # React contexts
│   └── AuthContext.js   # Authentication context
├── App.js              # Main app component
├── index.js            # App entry point
└── index.css           # Global styles
```

## Components Overview

### Authentication System

**Login Component (`Login.js`)**
- Secure login form
- JWT token management
- Demo credentials display
- Error handling

**Auth Context (`AuthContext.js`)**
- Global authentication state
- Token storage and validation
- Automatic token refresh
- Logout functionality

### Dashboard (`Dashboard.js`)

- Statistics overview
- Quick action buttons
- Recent evaluations table
- Performance metrics

### Employee Management (`EmployeeManagement.js`)

- Employee list with pagination
- Search and filter functionality
- Add/edit employee modal
- Soft delete operations
- Department management

### Evaluation Form (`EvaluationForm.js`)

- Step-by-step evaluation process
- Real-time score calculation
- Input validation
- Auto-calculation toggle
- Score preview with progress bars

### Evaluation Results (`EvaluationResults.js`)

- Results table with filtering
- Detailed evaluation modal
- Fuzzy logic visualization
- Report generation buttons
- Discipline level badges

### Reports (`Reports.js`)

- Report list and management
- Download functionality
- Report type filtering
- Usage statistics
- Instructions for report generation

## Features in Detail

### Real-time Score Calculation

The evaluation form automatically calculates scores based on input data:

```javascript
// Attendance Score
attendanceScore = (presentDays / totalWorkDays) * 100

// Punctuality Score  
punctualityScore = ((presentDays - lateArrivals) / presentDays) * 100

// Compliance Score (with violation penalty)
complianceScore = 100 - (violations * penalty_factor)
```

### Fuzzy Logic Integration

The frontend displays fuzzy logic results including:
- Input membership values
- Fuzzy rule activations
- Defuzzified output scores
- Confidence levels

### Responsive Design

- Mobile-first approach
- Bootstrap grid system
- Responsive tables and forms
- Touch-friendly interface

### Error Handling

- Global error boundaries
- API error handling
- User-friendly error messages
- Loading states and spinners

## API Integration

The frontend communicates with the backend API using Axios:

```javascript
// Authentication
axios.post('/api/auth/login', credentials)

// Employee management
axios.get('/api/employees', { params: filters })
axios.post('/api/employees', employeeData)

// Evaluations
axios.post('/api/evaluations', evaluationData)
axios.get('/api/evaluations', { params: filters })

// Reports
axios.post('/api/reports/generate', { evaluation_id })
axios.get('/api/reports/{id}/download', { responseType: 'blob' })
```

## State Management

### Authentication State

Managed by `AuthContext`:
- User information
- JWT token
- Login/logout functions
- Token validation

### Component State

Each component manages its own state:
- Form data
- Loading states
- Error messages
- Modal visibility

## Styling and UI

### Bootstrap Integration

- Consistent component styling
- Responsive grid system
- Pre-built UI components
- Theme customization

### Custom CSS Classes

```css
.discipline-badge { /* Custom badge styling */ }
.evaluation-form { /* Form container styling */ }
.results-container { /* Results display styling */ }
.fuzzy-visualization { /* Fuzzy logic charts */ }
```

### Color Coding

- **Success (Green)**: Sangat Disiplin, High scores
- **Info (Blue)**: Disiplin, Good scores
- **Warning (Yellow)**: Cukup, Medium scores
- **Danger (Red)**: Kurang, Low scores

## User Experience

### Navigation

- Intuitive menu structure
- Breadcrumb navigation
- Active page highlighting
- Mobile-responsive menu

### Forms

- Step-by-step process
- Real-time validation
- Auto-save functionality
- Clear error messages

### Data Display

- Sortable tables
- Pagination controls
- Search and filtering
- Export capabilities

## Testing

### Manual Testing Checklist

1. **Authentication**
   - [ ] Login with valid credentials
   - [ ] Login with invalid credentials
   - [ ] Token expiration handling
   - [ ] Logout functionality

2. **Employee Management**
   - [ ] Add new employee
   - [ ] Edit existing employee
   - [ ] Delete employee
   - [ ] Search employees
   - [ ] Filter by department

3. **Evaluation Process**
   - [ ] Create new evaluation
   - [ ] Auto-calculation works
   - [ ] Manual score override
   - [ ] Form validation
   - [ ] Fuzzy logic processing

4. **Results Display**
   - [ ] View evaluation results
   - [ ] Filter results
   - [ ] Generate reports
   - [ ] Download reports

### Browser Compatibility

Tested on:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Performance Optimization

### Code Splitting

- Route-based code splitting
- Lazy loading of components
- Dynamic imports

### API Optimization

- Request caching
- Pagination for large datasets
- Debounced search inputs
- Optimistic updates

### Bundle Optimization

```bash
# Analyze bundle size
npm run build
npx serve -s build
```

## Deployment

### Build for Production

```bash
npm run build
```

### Environment Variables

Create `.env.production` for production settings:

```env
REACT_APP_API_URL=https://your-api-domain.com
REACT_APP_VERSION=1.0.0
```

### Deployment Options

1. **Static Hosting** (Netlify, Vercel)
2. **CDN** (AWS CloudFront)
3. **Web Server** (Nginx, Apache)
4. **Container** (Docker)

### Example Nginx Configuration

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    location /api {
        proxy_pass http://backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Troubleshooting

### Common Issues

1. **API Connection Error**
   - Check backend server is running
   - Verify proxy configuration
   - Check CORS settings

2. **Authentication Issues**
   - Clear browser storage
   - Check token expiration
   - Verify API endpoints

3. **Build Errors**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Update dependencies

### Debug Mode

Enable debug logging:

```javascript
// In development
if (process.env.NODE_ENV === 'development') {
  console.log('Debug info:', data);
}
```

## Contributing

### Code Style

- Use ESLint configuration
- Follow React best practices
- Use functional components with hooks
- Implement proper error boundaries

### Component Guidelines

1. Keep components focused and small
2. Use proper prop types
3. Implement loading and error states
4. Follow naming conventions

### Git Workflow

1. Create feature branch
2. Make changes with clear commits
3. Test thoroughly
4. Submit pull request

## Future Enhancements

### Planned Features

- [ ] Dark mode support
- [ ] Multi-language support
- [ ] Advanced charts and analytics
- [ ] Real-time notifications
- [ ] Bulk operations
- [ ] Data export options
- [ ] Mobile app version

### Technical Improvements

- [ ] Unit test coverage
- [ ] E2E testing with Cypress
- [ ] Performance monitoring
- [ ] Accessibility improvements
- [ ] PWA capabilities
