{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\FUZYY LOGIC\\\\frontend\\\\src\\\\components\\\\Navigation.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navbar, Nav, Container, Button } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Navigation() {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const handleLogout = () => {\n    logout();\n  };\n  return /*#__PURE__*/_jsxDEV(Navbar, {\n    bg: \"primary\",\n    variant: \"dark\",\n    expand: \"lg\",\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [/*#__PURE__*/_jsxDEV(Navbar.Brand, {\n        as: Link,\n        to: \"/dashboard\",\n        children: \"Fuzzy Logic Discipline System\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navbar.Toggle, {\n        \"aria-controls\": \"basic-navbar-nav\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Navbar.Collapse, {\n        id: \"basic-navbar-nav\",\n        children: [/*#__PURE__*/_jsxDEV(Nav, {\n          className: \"me-auto\",\n          children: [/*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/dashboard\",\n            children: \"Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/employees\",\n            children: \"Karyawan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/evaluation\",\n            children: \"Evaluasi\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/results\",\n            children: \"Hasil\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Nav.Link, {\n            as: Link,\n            to: \"/reports\",\n            children: \"Laporan\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Nav, {\n          children: [/*#__PURE__*/_jsxDEV(Navbar.Text, {\n            className: \"me-3\",\n            children: [\"Welcome, \", user === null || user === void 0 ? void 0 : user.full_name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline-light\",\n            size: \"sm\",\n            onClick: handleLogout,\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n}\n_s(Navigation, \"SlSPRKmTohGnoLiiApupaRii2Oc=\", false, function () {\n  return [useAuth];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON>", "Nav", "Container", "<PERSON><PERSON>", "Link", "useAuth", "jsxDEV", "_jsxDEV", "Navigation", "_s", "user", "logout", "handleLogout", "bg", "variant", "expand", "children", "Brand", "as", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Toggle", "Collapse", "id", "className", "Text", "full_name", "size", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/src/components/Navigation.js"], "sourcesContent": ["import React from 'react';\nimport { Navbar, Nav, Container, Button } from 'react-bootstrap';\nimport { Link } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\n\nfunction Navigation() {\n  const { user, logout } = useAuth();\n\n  const handleLogout = () => {\n    logout();\n  };\n\n  return (\n    <Navbar bg=\"primary\" variant=\"dark\" expand=\"lg\">\n      <Container>\n        <Navbar.Brand as={Link} to=\"/dashboard\">\n          Fuzzy Logic Discipline System\n        </Navbar.Brand>\n        <Navbar.Toggle aria-controls=\"basic-navbar-nav\" />\n        <Navbar.Collapse id=\"basic-navbar-nav\">\n          <Nav className=\"me-auto\">\n            <Nav.Link as={Link} to=\"/dashboard\">Dashboard</Nav.Link>\n            <Nav.Link as={Link} to=\"/employees\">Karyawan</Nav.Link>\n            <Nav.Link as={Link} to=\"/evaluation\">Evaluasi</Nav.Link>\n            <Nav.Link as={Link} to=\"/results\">Hasil</Nav.Link>\n            <Nav.Link as={Link} to=\"/reports\">Laporan</Nav.Link>\n          </Nav>\n          <Nav>\n            <Navbar.Text className=\"me-3\">\n              Welcome, {user?.full_name}\n            </Navbar.Text>\n            <Button variant=\"outline-light\" size=\"sm\" onClick={handleLogout}>\n              Logout\n            </Button>\n          </Nav>\n        </Navbar.Collapse>\n      </Container>\n    </Navbar>\n  );\n}\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,iBAAiB;AAChE,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,SAASC,UAAUA,CAAA,EAAG;EAAAC,EAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGN,OAAO,CAAC,CAAC;EAElC,MAAMO,YAAY,GAAGA,CAAA,KAAM;IACzBD,MAAM,CAAC,CAAC;EACV,CAAC;EAED,oBACEJ,OAAA,CAACP,MAAM;IAACa,EAAE,EAAC,SAAS;IAACC,OAAO,EAAC,MAAM;IAACC,MAAM,EAAC,IAAI;IAAAC,QAAA,eAC7CT,OAAA,CAACL,SAAS;MAAAc,QAAA,gBACRT,OAAA,CAACP,MAAM,CAACiB,KAAK;QAACC,EAAE,EAAEd,IAAK;QAACe,EAAE,EAAC,YAAY;QAAAH,QAAA,EAAC;MAExC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eACfhB,OAAA,CAACP,MAAM,CAACwB,MAAM;QAAC,iBAAc;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClDhB,OAAA,CAACP,MAAM,CAACyB,QAAQ;QAACC,EAAE,EAAC,kBAAkB;QAAAV,QAAA,gBACpCT,OAAA,CAACN,GAAG;UAAC0B,SAAS,EAAC,SAAS;UAAAX,QAAA,gBACtBT,OAAA,CAACN,GAAG,CAACG,IAAI;YAACc,EAAE,EAAEd,IAAK;YAACe,EAAE,EAAC,YAAY;YAAAH,QAAA,EAAC;UAAS;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACxDhB,OAAA,CAACN,GAAG,CAACG,IAAI;YAACc,EAAE,EAAEd,IAAK;YAACe,EAAE,EAAC,YAAY;YAAAH,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACvDhB,OAAA,CAACN,GAAG,CAACG,IAAI;YAACc,EAAE,EAAEd,IAAK;YAACe,EAAE,EAAC,aAAa;YAAAH,QAAA,EAAC;UAAQ;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACxDhB,OAAA,CAACN,GAAG,CAACG,IAAI;YAACc,EAAE,EAAEd,IAAK;YAACe,EAAE,EAAC,UAAU;YAAAH,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAClDhB,OAAA,CAACN,GAAG,CAACG,IAAI;YAACc,EAAE,EAAEd,IAAK;YAACe,EAAE,EAAC,UAAU;YAAAH,QAAA,EAAC;UAAO;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNhB,OAAA,CAACN,GAAG;UAAAe,QAAA,gBACFT,OAAA,CAACP,MAAM,CAAC4B,IAAI;YAACD,SAAS,EAAC,MAAM;YAAAX,QAAA,GAAC,WACnB,EAACN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,SAAS;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACdhB,OAAA,CAACJ,MAAM;YAACW,OAAO,EAAC,eAAe;YAACgB,IAAI,EAAC,IAAI;YAACC,OAAO,EAAEnB,YAAa;YAAAI,QAAA,EAAC;UAEjE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEb;AAACd,EAAA,CAlCQD,UAAU;EAAA,QACQH,OAAO;AAAA;AAAA2B,EAAA,GADzBxB,UAAU;AAoCnB,eAAeA,UAAU;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}