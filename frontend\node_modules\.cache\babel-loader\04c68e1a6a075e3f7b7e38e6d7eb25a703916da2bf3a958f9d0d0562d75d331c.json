{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport Feedback from './Feedback';\nimport FormCheckInput from './FormCheckInput';\nimport Form<PERSON>heckLabel from './FormCheckLabel';\nimport Form<PERSON>ontext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { hasChildOfType } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FormCheck = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  bsSwitchPrefix,\n  inline = false,\n  reverse = false,\n  disabled = false,\n  isValid = false,\n  isInvalid = false,\n  feedbackTooltip = false,\n  feedback,\n  feedbackType,\n  className,\n  style,\n  title = '',\n  type = 'checkbox',\n  label,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as = 'input',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check');\n  bsSwitchPrefix = useBootstrapPrefix(bsSwitchPrefix, 'form-switch');\n  const {\n    controlId\n  } = useContext(FormContext);\n  const innerFormContext = useMemo(() => ({\n    controlId: id || controlId\n  }), [controlId, id]);\n  const hasLabel = !children && label != null && label !== false || hasChildOfType(children, FormCheckLabel);\n  const input = /*#__PURE__*/_jsx(FormCheckInput, {\n    ...props,\n    type: type === 'switch' ? 'checkbox' : type,\n    ref: ref,\n    isValid: isValid,\n    isInvalid: isInvalid,\n    disabled: disabled,\n    as: as\n  });\n  return /*#__PURE__*/_jsx(FormContext.Provider, {\n    value: innerFormContext,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: style,\n      className: classNames(className, hasLabel && bsPrefix, inline && `${bsPrefix}-inline`, reverse && `${bsPrefix}-reverse`, type === 'switch' && bsSwitchPrefix),\n      children: children || /*#__PURE__*/_jsxs(_Fragment, {\n        children: [input, hasLabel && /*#__PURE__*/_jsx(FormCheckLabel, {\n          title: title,\n          children: label\n        }), feedback && /*#__PURE__*/_jsx(Feedback, {\n          type: feedbackType,\n          tooltip: feedbackTooltip,\n          children: feedback\n        })]\n      })\n    })\n  });\n});\nFormCheck.displayName = 'FormCheck';\nexport default Object.assign(FormCheck, {\n  Input: FormCheckInput,\n  Label: FormCheckLabel\n});", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useMemo", "<PERSON><PERSON><PERSON>", "FormCheckInput", "FormCheckLabel", "FormContext", "useBootstrapPrefix", "hasChildOfType", "jsx", "_jsx", "Fragment", "_Fragment", "jsxs", "_jsxs", "FormCheck", "forwardRef", "id", "bsPrefix", "bsSwitchPrefix", "inline", "reverse", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "feedbackTooltip", "feedback", "feedbackType", "className", "style", "title", "type", "label", "children", "as", "props", "ref", "controlId", "innerFormContext", "<PERSON><PERSON><PERSON><PERSON>", "input", "Provider", "value", "tooltip", "displayName", "Object", "assign", "Input", "Label"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/FormCheck.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nimport Feedback from './Feedback';\nimport FormCheckInput from './FormCheckInput';\nimport Form<PERSON>heckLabel from './FormCheckLabel';\nimport Form<PERSON>ontext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { hasChildOfType } from './ElementChildren';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst FormCheck = /*#__PURE__*/React.forwardRef(({\n  id,\n  bsPrefix,\n  bsSwitchPrefix,\n  inline = false,\n  reverse = false,\n  disabled = false,\n  isValid = false,\n  isInvalid = false,\n  feedbackTooltip = false,\n  feedback,\n  feedbackType,\n  className,\n  style,\n  title = '',\n  type = 'checkbox',\n  label,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as = 'input',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-check');\n  bsSwitchPrefix = useBootstrapPrefix(bsSwitchPrefix, 'form-switch');\n  const {\n    controlId\n  } = useContext(FormContext);\n  const innerFormContext = useMemo(() => ({\n    controlId: id || controlId\n  }), [controlId, id]);\n  const hasLabel = !children && label != null && label !== false || hasChildOfType(children, FormCheckLabel);\n  const input = /*#__PURE__*/_jsx(FormCheckInput, {\n    ...props,\n    type: type === 'switch' ? 'checkbox' : type,\n    ref: ref,\n    isValid: isValid,\n    isInvalid: isInvalid,\n    disabled: disabled,\n    as: as\n  });\n  return /*#__PURE__*/_jsx(FormContext.Provider, {\n    value: innerFormContext,\n    children: /*#__PURE__*/_jsx(\"div\", {\n      style: style,\n      className: classNames(className, hasLabel && bsPrefix, inline && `${bsPrefix}-inline`, reverse && `${bsPrefix}-reverse`, type === 'switch' && bsSwitchPrefix),\n      children: children || /*#__PURE__*/_jsxs(_Fragment, {\n        children: [input, hasLabel && /*#__PURE__*/_jsx(FormCheckLabel, {\n          title: title,\n          children: label\n        }), feedback && /*#__PURE__*/_jsx(Feedback, {\n          type: feedbackType,\n          tooltip: feedbackTooltip,\n          children: feedback\n        })]\n      })\n    })\n  });\n});\nFormCheck.displayName = 'FormCheck';\nexport default Object.assign(FormCheck, {\n  Input: FormCheckInput,\n  Label: FormCheckLabel\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQ,IAAIC,SAAS,QAAQ,mBAAmB;AACzD,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,SAAS,GAAG,aAAaf,KAAK,CAACgB,UAAU,CAAC,CAAC;EAC/CC,EAAE;EACFC,QAAQ;EACRC,cAAc;EACdC,MAAM,GAAG,KAAK;EACdC,OAAO,GAAG,KAAK;EACfC,QAAQ,GAAG,KAAK;EAChBC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,KAAK;EACjBC,eAAe,GAAG,KAAK;EACvBC,QAAQ;EACRC,YAAY;EACZC,SAAS;EACTC,KAAK;EACLC,KAAK,GAAG,EAAE;EACVC,IAAI,GAAG,UAAU;EACjBC,KAAK;EACLC,QAAQ;EACR;EACAC,EAAE,GAAG,OAAO;EACZ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTlB,QAAQ,GAAGX,kBAAkB,CAACW,QAAQ,EAAE,YAAY,CAAC;EACrDC,cAAc,GAAGZ,kBAAkB,CAACY,cAAc,EAAE,aAAa,CAAC;EAClE,MAAM;IACJkB;EACF,CAAC,GAAGpC,UAAU,CAACK,WAAW,CAAC;EAC3B,MAAMgC,gBAAgB,GAAGpC,OAAO,CAAC,OAAO;IACtCmC,SAAS,EAAEpB,EAAE,IAAIoB;EACnB,CAAC,CAAC,EAAE,CAACA,SAAS,EAAEpB,EAAE,CAAC,CAAC;EACpB,MAAMsB,QAAQ,GAAG,CAACN,QAAQ,IAAID,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,KAAK,IAAIxB,cAAc,CAACyB,QAAQ,EAAE5B,cAAc,CAAC;EAC1G,MAAMmC,KAAK,GAAG,aAAa9B,IAAI,CAACN,cAAc,EAAE;IAC9C,GAAG+B,KAAK;IACRJ,IAAI,EAAEA,IAAI,KAAK,QAAQ,GAAG,UAAU,GAAGA,IAAI;IAC3CK,GAAG,EAAEA,GAAG;IACRb,OAAO,EAAEA,OAAO;IAChBC,SAAS,EAAEA,SAAS;IACpBF,QAAQ,EAAEA,QAAQ;IAClBY,EAAE,EAAEA;EACN,CAAC,CAAC;EACF,OAAO,aAAaxB,IAAI,CAACJ,WAAW,CAACmC,QAAQ,EAAE;IAC7CC,KAAK,EAAEJ,gBAAgB;IACvBL,QAAQ,EAAE,aAAavB,IAAI,CAAC,KAAK,EAAE;MACjCmB,KAAK,EAAEA,KAAK;MACZD,SAAS,EAAE7B,UAAU,CAAC6B,SAAS,EAAEW,QAAQ,IAAIrB,QAAQ,EAAEE,MAAM,IAAI,GAAGF,QAAQ,SAAS,EAAEG,OAAO,IAAI,GAAGH,QAAQ,UAAU,EAAEa,IAAI,KAAK,QAAQ,IAAIZ,cAAc,CAAC;MAC7Jc,QAAQ,EAAEA,QAAQ,IAAI,aAAanB,KAAK,CAACF,SAAS,EAAE;QAClDqB,QAAQ,EAAE,CAACO,KAAK,EAAED,QAAQ,IAAI,aAAa7B,IAAI,CAACL,cAAc,EAAE;UAC9DyB,KAAK,EAAEA,KAAK;UACZG,QAAQ,EAAED;QACZ,CAAC,CAAC,EAAEN,QAAQ,IAAI,aAAahB,IAAI,CAACP,QAAQ,EAAE;UAC1C4B,IAAI,EAAEJ,YAAY;UAClBgB,OAAO,EAAElB,eAAe;UACxBQ,QAAQ,EAAEP;QACZ,CAAC,CAAC;MACJ,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFX,SAAS,CAAC6B,WAAW,GAAG,WAAW;AACnC,eAAeC,MAAM,CAACC,MAAM,CAAC/B,SAAS,EAAE;EACtCgC,KAAK,EAAE3C,cAAc;EACrB4C,KAAK,EAAE3C;AACT,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}