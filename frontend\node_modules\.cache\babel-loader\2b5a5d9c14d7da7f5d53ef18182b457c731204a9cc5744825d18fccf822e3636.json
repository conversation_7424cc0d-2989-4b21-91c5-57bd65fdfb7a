{"ast": null, "code": "import * as React from 'react';\n\n/**\n * Iterates through children that are typically specified as `props.children`,\n * but only maps over children that are \"valid elements\".\n *\n * The mapFunction provided index will be normalised to the components mapped,\n * so an invalid component would not increase the index.\n *\n */\nfunction map(children, func) {\n  let index = 0;\n  return React.Children.map(children, child => /*#__PURE__*/React.isValidElement(child) ? func(child, index++) : child);\n}\n\n/**\n * Iterates through children that are \"valid elements\".\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child with the index reflecting the position relative to \"valid components\".\n */\nfunction forEach(children, func) {\n  let index = 0;\n  React.Children.forEach(children, child => {\n    if (/*#__PURE__*/React.isValidElement(child)) func(child, index++);\n  });\n}\n\n/**\n * Finds whether a component's `children` prop includes a React element of the\n * specified type.\n */\nfunction hasChildOfType(children, type) {\n  return React.Children.toArray(children).some(child => /*#__PURE__*/React.isValidElement(child) && child.type === type);\n}\nexport { map, forEach, hasChildOfType };", "map": {"version": 3, "names": ["React", "map", "children", "func", "index", "Children", "child", "isValidElement", "for<PERSON>ach", "hasChildOfType", "type", "toArray", "some"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/ElementChildren.js"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Iterates through children that are typically specified as `props.children`,\n * but only maps over children that are \"valid elements\".\n *\n * The mapFunction provided index will be normalised to the components mapped,\n * so an invalid component would not increase the index.\n *\n */\nfunction map(children, func) {\n  let index = 0;\n  return React.Children.map(children, child => /*#__PURE__*/React.isValidElement(child) ? func(child, index++) : child);\n}\n\n/**\n * Iterates through children that are \"valid elements\".\n *\n * The provided forEachFunc(child, index) will be called for each\n * leaf child with the index reflecting the position relative to \"valid components\".\n */\nfunction forEach(children, func) {\n  let index = 0;\n  React.Children.forEach(children, child => {\n    if ( /*#__PURE__*/React.isValidElement(child)) func(child, index++);\n  });\n}\n\n/**\n * Finds whether a component's `children` prop includes a React element of the\n * specified type.\n */\nfunction hasChildOfType(children, type) {\n  return React.Children.toArray(children).some(child => /*#__PURE__*/React.isValidElement(child) && child.type === type);\n}\nexport { map, forEach, hasChildOfType };"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EAC3B,IAAIC,KAAK,GAAG,CAAC;EACb,OAAOJ,KAAK,CAACK,QAAQ,CAACJ,GAAG,CAACC,QAAQ,EAAEI,KAAK,IAAI,aAAaN,KAAK,CAACO,cAAc,CAACD,KAAK,CAAC,GAAGH,IAAI,CAACG,KAAK,EAAEF,KAAK,EAAE,CAAC,GAAGE,KAAK,CAAC;AACvH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,OAAOA,CAACN,QAAQ,EAAEC,IAAI,EAAE;EAC/B,IAAIC,KAAK,GAAG,CAAC;EACbJ,KAAK,CAACK,QAAQ,CAACG,OAAO,CAACN,QAAQ,EAAEI,KAAK,IAAI;IACxC,IAAK,aAAaN,KAAK,CAACO,cAAc,CAACD,KAAK,CAAC,EAAEH,IAAI,CAACG,KAAK,EAAEF,KAAK,EAAE,CAAC;EACrE,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASK,cAAcA,CAACP,QAAQ,EAAEQ,IAAI,EAAE;EACtC,OAAOV,KAAK,CAACK,QAAQ,CAACM,OAAO,CAACT,QAAQ,CAAC,CAACU,IAAI,CAACN,KAAK,IAAI,aAAaN,KAAK,CAACO,cAAc,CAACD,KAAK,CAAC,IAAIA,KAAK,CAACI,IAAI,KAAKA,IAAI,CAAC;AACxH;AACA,SAAST,GAAG,EAAEO,OAAO,EAAEC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}