{"ast": null, "code": "import * as React from 'react';\nconst NavContext = /*#__PURE__*/React.createContext(null);\nNavContext.displayName = 'NavContext';\nexport default NavContext;", "map": {"version": 3, "names": ["React", "NavContext", "createContext", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/@restart/ui/esm/NavContext.js"], "sourcesContent": ["import * as React from 'react';\nconst NavContext = /*#__PURE__*/React.createContext(null);\nNavContext.displayName = 'NavContext';\nexport default NavContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,UAAU,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACzDD,UAAU,CAACE,WAAW,GAAG,YAAY;AACrC,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}