{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\FUZYY LOGIC\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport Navigation from './components/Navigation';\nimport Login from './components/Login';\nimport Dashboard from './components/Dashboard';\nimport EmployeeManagement from './components/EmployeeManagement';\nimport EvaluationForm from './components/EvaluationForm';\nimport EvaluationResults from './components/EvaluationResults';\nimport Reports from './components/Reports';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nfunction AppContent() {\n  _s();\n  const {\n    user,\n    loading\n  } = useAuth();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: \"d-flex justify-content-center align-items-center\",\n      style: {\n        height: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner-border text-primary\",\n        role: \"status\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"visually-hidden\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [user && /*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 16\n    }, this), /*#__PURE__*/_jsxDEV(Container, {\n      fluid: true,\n      className: user ? \"mt-4\" : \"\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 60\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: user ? /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 45\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/employees\",\n          element: user ? /*#__PURE__*/_jsxDEV(EmployeeManagement, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 54\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/evaluation\",\n          element: user ? /*#__PURE__*/_jsxDEV(EvaluationForm, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 50\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/results\",\n          element: user ? /*#__PURE__*/_jsxDEV(EvaluationResults, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 53\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/reports\",\n          element: user ? /*#__PURE__*/_jsxDEV(Reports, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: user ? /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 60\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(AppContent, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function () {\n  return [useAuth];\n});\n_c2 = AppContent;\nexport default App;\nvar _c, _c2;\n$RefreshReg$(_c, \"App\");\n$RefreshReg$(_c2, \"AppContent\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Container", "Navigation", "<PERSON><PERSON>", "Dashboard", "EmployeeManagement", "EvaluationForm", "EvaluationResults", "Reports", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "children", "className", "A<PERSON><PERSON><PERSON>nt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "_s", "user", "loading", "style", "height", "role", "fluid", "path", "element", "to", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/src/App.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Container } from 'react-bootstrap';\nimport Navigation from './components/Navigation';\nimport Login from './components/Login';\nimport Dashboard from './components/Dashboard';\nimport EmployeeManagement from './components/EmployeeManagement';\nimport EvaluationForm from './components/EvaluationForm';\nimport EvaluationResults from './components/EvaluationResults';\nimport Reports from './components/Reports';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Router>\n        <div className=\"App\">\n          <AppContent />\n        </div>\n      </Router>\n    </AuthProvider>\n  );\n}\n\nfunction AppContent() {\n  const { user, loading } = useAuth();\n\n  if (loading) {\n    return (\n      <Container className=\"d-flex justify-content-center align-items-center\" style={{ height: '100vh' }}>\n        <div className=\"spinner-border text-primary\" role=\"status\">\n          <span className=\"visually-hidden\">Loading...</span>\n        </div>\n      </Container>\n    );\n  }\n\n  return (\n    <>\n      {user && <Navigation />}\n      <Container fluid className={user ? \"mt-4\" : \"\"}>\n        <Routes>\n          <Route \n            path=\"/login\" \n            element={user ? <Navigate to=\"/dashboard\" /> : <Login />} \n          />\n          <Route \n            path=\"/dashboard\" \n            element={user ? <Dashboard /> : <Navigate to=\"/login\" />} \n          />\n          <Route \n            path=\"/employees\" \n            element={user ? <EmployeeManagement /> : <Navigate to=\"/login\" />} \n          />\n          <Route \n            path=\"/evaluation\" \n            element={user ? <EvaluationForm /> : <Navigate to=\"/login\" />} \n          />\n          <Route \n            path=\"/results\" \n            element={user ? <EvaluationResults /> : <Navigate to=\"/login\" />} \n          />\n          <Route \n            path=\"/reports\" \n            element={user ? <Reports /> : <Navigate to=\"/login\" />} \n          />\n          <Route \n            path=\"/\" \n            element={user ? <Navigate to=\"/dashboard\" /> : <Navigate to=\"/login\" />} \n          />\n        </Routes>\n      </Container>\n    </>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,kBAAkB,MAAM,iCAAiC;AAChE,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,SAASC,YAAY,EAAEC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE/D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEH,OAAA,CAACH,YAAY;IAAAO,QAAA,eACXJ,OAAA,CAACf,MAAM;MAAAmB,QAAA,eACLJ,OAAA;QAAKK,SAAS,EAAC,KAAK;QAAAD,QAAA,eAClBJ,OAAA,CAACM,UAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACC,EAAA,GAVQR,GAAG;AAYZ,SAASG,UAAUA,CAAA,EAAG;EAAAM,EAAA;EACpB,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAEnC,IAAIgB,OAAO,EAAE;IACX,oBACEd,OAAA,CAACX,SAAS;MAACgB,SAAS,EAAC,kDAAkD;MAACU,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAZ,QAAA,eACjGJ,OAAA;QAAKK,SAAS,EAAC,6BAA6B;QAACY,IAAI,EAAC,QAAQ;QAAAb,QAAA,eACxDJ,OAAA;UAAMK,SAAS,EAAC,iBAAiB;UAAAD,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACEV,OAAA,CAAAE,SAAA;IAAAE,QAAA,GACGS,IAAI,iBAAIb,OAAA,CAACV,UAAU;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACvBV,OAAA,CAACX,SAAS;MAAC6B,KAAK;MAACb,SAAS,EAAEQ,IAAI,GAAG,MAAM,GAAG,EAAG;MAAAT,QAAA,eAC7CJ,OAAA,CAACd,MAAM;QAAAkB,QAAA,gBACLJ,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,QAAQ;UACbC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACT,KAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACFV,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,YAAY;UACjBC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACR,SAAS;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACFV,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,YAAY;UACjBC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACP,kBAAkB;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACFV,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,aAAa;UAClBC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACN,cAAc;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eACFV,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,UAAU;UACfC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACL,iBAAiB;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eACFV,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,UAAU;UACfC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACJ,OAAO;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eACFV,OAAA,CAACb,KAAK;UACJgC,IAAI,EAAC,GAAG;UACRC,OAAO,EAAEP,IAAI,gBAAGb,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAY;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGV,OAAA,CAACZ,QAAQ;YAACiC,EAAE,EAAC;UAAQ;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA,eACZ,CAAC;AAEP;AAACE,EAAA,CAlDQN,UAAU;EAAA,QACSR,OAAO;AAAA;AAAAwB,GAAA,GAD1BhB,UAAU;AAoDnB,eAAeH,GAAG;AAAC,IAAAQ,EAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAZ,EAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}