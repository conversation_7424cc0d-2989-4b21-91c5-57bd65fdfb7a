{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\FUZYY LOGIC\\\\frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\nexport function useAuth() {\n  _s();\n  return useContext(AuthContext);\n}\n_s(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport function AuthProvider({\n  children\n}) {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Set up axios defaults\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n  }, []);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // Verify token with backend\n      axios.get('/api/auth/verify').then(response => {\n        setUser(response.data.user);\n      }).catch(() => {\n        // Token is invalid, remove it\n        localStorage.removeItem('token');\n        delete axios.defaults.headers.common['Authorization'];\n      }).finally(() => {\n        setLoading(false);\n      });\n    } else {\n      setLoading(false);\n    }\n  }, []);\n  const login = async (username, password) => {\n    try {\n      const response = await axios.post('/api/auth/login', {\n        username,\n        password\n      });\n      const {\n        token,\n        user\n      } = response.data;\n\n      // Store token\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n\n      // Set user\n      setUser(user);\n      toast.success('Login berhasil!');\n      return {\n        success: true\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || 'Login gagal';\n      toast.error(message);\n      return {\n        success: false,\n        error: message\n      };\n    }\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    setUser(null);\n    toast.info('Anda telah logout');\n  };\n  const value = {\n    user,\n    login,\n    logout,\n    loading\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n}\n_s2(AuthProvider, \"Ui6DLq5vMU2GgsRY6PPBrtV3hI8=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "useAuth", "_s", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "localStorage", "getItem", "defaults", "headers", "common", "get", "then", "response", "data", "catch", "removeItem", "finally", "login", "username", "password", "post", "setItem", "success", "error", "_error$response", "_error$response$data", "message", "logout", "info", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { toast } from 'react-toastify';\n\nconst AuthContext = createContext();\n\nexport function useAuth() {\n  return useContext(AuthContext);\n}\n\nexport function AuthProvider({ children }) {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Set up axios defaults\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n  }, []);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      // Verify token with backend\n      axios.get('/api/auth/verify')\n        .then(response => {\n          setUser(response.data.user);\n        })\n        .catch(() => {\n          // Token is invalid, remove it\n          localStorage.removeItem('token');\n          delete axios.defaults.headers.common['Authorization'];\n        })\n        .finally(() => {\n          setLoading(false);\n        });\n    } else {\n      setLoading(false);\n    }\n  }, []);\n\n  const login = async (username, password) => {\n    try {\n      const response = await axios.post('/api/auth/login', {\n        username,\n        password\n      });\n\n      const { token, user } = response.data;\n      \n      // Store token\n      localStorage.setItem('token', token);\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n      \n      // Set user\n      setUser(user);\n      \n      toast.success('Login berhasil!');\n      return { success: true };\n    } catch (error) {\n      const message = error.response?.data?.error || 'Login gagal';\n      toast.error(message);\n      return { success: false, error: message };\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    delete axios.defaults.headers.common['Authorization'];\n    setUser(null);\n    toast.info('Anda telah logout');\n  };\n\n  const value = {\n    user,\n    login,\n    logout,\n    loading\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;AAEnC,OAAO,SAASS,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACxB,OAAOT,UAAU,CAACO,WAAW,CAAC;AAChC;AAACE,EAAA,CAFeD,OAAO;AAIvB,OAAO,SAASE,YAAYA,CAAC;EAAEC;AAAS,CAAC,EAAE;EAAAC,GAAA;EACzC,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACTd,KAAK,CAACiB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;IACpE;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAf,SAAS,CAAC,MAAM;IACd,MAAMe,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAIF,KAAK,EAAE;MACT;MACAd,KAAK,CAACoB,GAAG,CAAC,kBAAkB,CAAC,CAC1BC,IAAI,CAACC,QAAQ,IAAI;QAChBX,OAAO,CAACW,QAAQ,CAACC,IAAI,CAACb,IAAI,CAAC;MAC7B,CAAC,CAAC,CACDc,KAAK,CAAC,MAAM;QACX;QACAT,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;QAChC,OAAOzB,KAAK,CAACiB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;MACvD,CAAC,CAAC,CACDO,OAAO,CAAC,MAAM;QACbb,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,CAAC;IACN,CAAC,MAAM;MACLA,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMc,KAAK,GAAG,MAAAA,CAAOC,QAAQ,EAAEC,QAAQ,KAAK;IAC1C,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMtB,KAAK,CAAC8B,IAAI,CAAC,iBAAiB,EAAE;QACnDF,QAAQ;QACRC;MACF,CAAC,CAAC;MAEF,MAAM;QAAEf,KAAK;QAAEJ;MAAK,CAAC,GAAGY,QAAQ,CAACC,IAAI;;MAErC;MACAR,YAAY,CAACgB,OAAO,CAAC,OAAO,EAAEjB,KAAK,CAAC;MACpCd,KAAK,CAACiB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUL,KAAK,EAAE;;MAElE;MACAH,OAAO,CAACD,IAAI,CAAC;MAEbT,KAAK,CAAC+B,OAAO,CAAC,iBAAiB,CAAC;MAChC,OAAO;QAAEA,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACd,MAAMC,OAAO,GAAG,EAAAF,eAAA,GAAAD,KAAK,CAACX,QAAQ,cAAAY,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBX,IAAI,cAAAY,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAI,aAAa;MAC5DhC,KAAK,CAACgC,KAAK,CAACG,OAAO,CAAC;MACpB,OAAO;QAAEJ,OAAO,EAAE,KAAK;QAAEC,KAAK,EAAEG;MAAQ,CAAC;IAC3C;EACF,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBtB,YAAY,CAACU,UAAU,CAAC,OAAO,CAAC;IAChC,OAAOzB,KAAK,CAACiB,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACrDR,OAAO,CAAC,IAAI,CAAC;IACbV,KAAK,CAACqC,IAAI,CAAC,mBAAmB,CAAC;EACjC,CAAC;EAED,MAAMC,KAAK,GAAG;IACZ7B,IAAI;IACJiB,KAAK;IACLU,MAAM;IACNzB;EACF,CAAC;EAED,oBACET,OAAA,CAACC,WAAW,CAACoC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA/B,QAAA,EAChCA;EAAQ;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B;AAACnC,GAAA,CA9EeF,YAAY;AAAAsC,EAAA,GAAZtC,YAAY;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}