{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});", "map": {"version": 3, "names": ["classNames", "React", "useUncontrolled", "useEventCallback", "useBootstrapPrefix", "AlertHeading", "AlertLink", "Fade", "CloseButton", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "forwardRef", "uncontrolledProps", "ref", "bsPrefix", "show", "<PERSON><PERSON><PERSON><PERSON>", "closeVariant", "className", "children", "variant", "onClose", "dismissible", "transition", "props", "prefix", "handleClose", "e", "Transition", "alert", "role", "undefined", "onClick", "unmountOnExit", "in", "displayName", "Object", "assign", "Link", "Heading"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/Alert.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AlertHeading from './AlertHeading';\nimport AlertLink from './AlertLink';\nimport Fade from './Fade';\nimport CloseButton from './CloseButton';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Alert = /*#__PURE__*/React.forwardRef((uncontrolledProps, ref) => {\n  const {\n    bsPrefix,\n    show = true,\n    closeLabel = 'Close alert',\n    closeVariant,\n    className,\n    children,\n    variant = 'primary',\n    onClose,\n    dismissible,\n    transition = Fade,\n    ...props\n  } = useUncontrolled(uncontrolledProps, {\n    show: 'onClose'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'alert');\n  const handleClose = useEventCallback(e => {\n    if (onClose) {\n      onClose(false, e);\n    }\n  });\n  const Transition = transition === true ? Fade : transition;\n  const alert = /*#__PURE__*/_jsxs(\"div\", {\n    role: \"alert\",\n    ...(!Transition ? props : undefined),\n    ref: ref,\n    className: classNames(className, prefix, variant && `${prefix}-${variant}`, dismissible && `${prefix}-dismissible`),\n    children: [dismissible && /*#__PURE__*/_jsx(CloseButton, {\n      onClick: handleClose,\n      \"aria-label\": closeLabel,\n      variant: closeVariant\n    }), children]\n  });\n  if (!Transition) return show ? alert : null;\n  return /*#__PURE__*/_jsx(Transition, {\n    unmountOnExit: true,\n    ...props,\n    ref: undefined,\n    in: show,\n    children: alert\n  });\n});\nAlert.displayName = 'Alert';\nexport default Object.assign(Alert, {\n  Link: AlertLink,\n  Heading: AlertHeading\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,KAAK,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,CAACC,iBAAiB,EAAEC,GAAG,KAAK;EACtE,MAAM;IACJC,QAAQ;IACRC,IAAI,GAAG,IAAI;IACXC,UAAU,GAAG,aAAa;IAC1BC,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRC,OAAO,GAAG,SAAS;IACnBC,OAAO;IACPC,WAAW;IACXC,UAAU,GAAGnB,IAAI;IACjB,GAAGoB;EACL,CAAC,GAAGzB,eAAe,CAACa,iBAAiB,EAAE;IACrCG,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMU,MAAM,GAAGxB,kBAAkB,CAACa,QAAQ,EAAE,OAAO,CAAC;EACpD,MAAMY,WAAW,GAAG1B,gBAAgB,CAAC2B,CAAC,IAAI;IACxC,IAAIN,OAAO,EAAE;MACXA,OAAO,CAAC,KAAK,EAAEM,CAAC,CAAC;IACnB;EACF,CAAC,CAAC;EACF,MAAMC,UAAU,GAAGL,UAAU,KAAK,IAAI,GAAGnB,IAAI,GAAGmB,UAAU;EAC1D,MAAMM,KAAK,GAAG,aAAapB,KAAK,CAAC,KAAK,EAAE;IACtCqB,IAAI,EAAE,OAAO;IACb,IAAI,CAACF,UAAU,GAAGJ,KAAK,GAAGO,SAAS,CAAC;IACpClB,GAAG,EAAEA,GAAG;IACRK,SAAS,EAAErB,UAAU,CAACqB,SAAS,EAAEO,MAAM,EAAEL,OAAO,IAAI,GAAGK,MAAM,IAAIL,OAAO,EAAE,EAAEE,WAAW,IAAI,GAAGG,MAAM,cAAc,CAAC;IACnHN,QAAQ,EAAE,CAACG,WAAW,IAAI,aAAaf,IAAI,CAACF,WAAW,EAAE;MACvD2B,OAAO,EAAEN,WAAW;MACpB,YAAY,EAAEV,UAAU;MACxBI,OAAO,EAAEH;IACX,CAAC,CAAC,EAAEE,QAAQ;EACd,CAAC,CAAC;EACF,IAAI,CAACS,UAAU,EAAE,OAAOb,IAAI,GAAGc,KAAK,GAAG,IAAI;EAC3C,OAAO,aAAatB,IAAI,CAACqB,UAAU,EAAE;IACnCK,aAAa,EAAE,IAAI;IACnB,GAAGT,KAAK;IACRX,GAAG,EAAEkB,SAAS;IACdG,EAAE,EAAEnB,IAAI;IACRI,QAAQ,EAAEU;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFnB,KAAK,CAACyB,WAAW,GAAG,OAAO;AAC3B,eAAeC,MAAM,CAACC,MAAM,CAAC3B,KAAK,EAAE;EAClC4B,IAAI,EAAEnC,SAAS;EACfoC,OAAO,EAAErC;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}