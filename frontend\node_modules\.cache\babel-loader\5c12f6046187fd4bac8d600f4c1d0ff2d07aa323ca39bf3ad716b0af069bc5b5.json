{"ast": null, "code": "import css from './css';\nimport listen from './listen';\nimport triggerEvent from './triggerEvent';\nfunction parseDuration(node) {\n  var str = css(node, 'transitionDuration') || '';\n  var mult = str.indexOf('ms') === -1 ? 1000 : 1;\n  return parseFloat(str) * mult;\n}\nfunction emulateTransitionEnd(element, duration, padding) {\n  if (padding === void 0) {\n    padding = 5;\n  }\n  var called = false;\n  var handle = setTimeout(function () {\n    if (!called) triggerEvent(element, 'transitionend', true);\n  }, duration + padding);\n  var remove = listen(element, 'transitionend', function () {\n    called = true;\n  }, {\n    once: true\n  });\n  return function () {\n    clearTimeout(handle);\n    remove();\n  };\n}\nexport default function transitionEnd(element, handler, duration, padding) {\n  if (duration == null) duration = parseDuration(element) || 0;\n  var removeEmulate = emulateTransitionEnd(element, duration, padding);\n  var remove = listen(element, 'transitionend', handler);\n  return function () {\n    removeEmulate();\n    remove();\n  };\n}", "map": {"version": 3, "names": ["css", "listen", "triggerEvent", "parseDuration", "node", "str", "mult", "indexOf", "parseFloat", "emulateTransitionEnd", "element", "duration", "padding", "called", "handle", "setTimeout", "remove", "once", "clearTimeout", "transitionEnd", "handler", "removeEmulate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/dom-helpers/esm/transitionEnd.js"], "sourcesContent": ["import css from './css';\nimport listen from './listen';\nimport triggerEvent from './triggerEvent';\n\nfunction parseDuration(node) {\n  var str = css(node, 'transitionDuration') || '';\n  var mult = str.indexOf('ms') === -1 ? 1000 : 1;\n  return parseFloat(str) * mult;\n}\n\nfunction emulateTransitionEnd(element, duration, padding) {\n  if (padding === void 0) {\n    padding = 5;\n  }\n\n  var called = false;\n  var handle = setTimeout(function () {\n    if (!called) triggerEvent(element, 'transitionend', true);\n  }, duration + padding);\n  var remove = listen(element, 'transitionend', function () {\n    called = true;\n  }, {\n    once: true\n  });\n  return function () {\n    clearTimeout(handle);\n    remove();\n  };\n}\n\nexport default function transitionEnd(element, handler, duration, padding) {\n  if (duration == null) duration = parseDuration(element) || 0;\n  var removeEmulate = emulateTransitionEnd(element, duration, padding);\n  var remove = listen(element, 'transitionend', handler);\n  return function () {\n    removeEmulate();\n    remove();\n  };\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,YAAY,MAAM,gBAAgB;AAEzC,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAIC,GAAG,GAAGL,GAAG,CAACI,IAAI,EAAE,oBAAoB,CAAC,IAAI,EAAE;EAC/C,IAAIE,IAAI,GAAGD,GAAG,CAACE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;EAC9C,OAAOC,UAAU,CAACH,GAAG,CAAC,GAAGC,IAAI;AAC/B;AAEA,SAASG,oBAAoBA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACxD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC;EACb;EAEA,IAAIC,MAAM,GAAG,KAAK;EAClB,IAAIC,MAAM,GAAGC,UAAU,CAAC,YAAY;IAClC,IAAI,CAACF,MAAM,EAAEX,YAAY,CAACQ,OAAO,EAAE,eAAe,EAAE,IAAI,CAAC;EAC3D,CAAC,EAAEC,QAAQ,GAAGC,OAAO,CAAC;EACtB,IAAII,MAAM,GAAGf,MAAM,CAACS,OAAO,EAAE,eAAe,EAAE,YAAY;IACxDG,MAAM,GAAG,IAAI;EACf,CAAC,EAAE;IACDI,IAAI,EAAE;EACR,CAAC,CAAC;EACF,OAAO,YAAY;IACjBC,YAAY,CAACJ,MAAM,CAAC;IACpBE,MAAM,CAAC,CAAC;EACV,CAAC;AACH;AAEA,eAAe,SAASG,aAAaA,CAACT,OAAO,EAAEU,OAAO,EAAET,QAAQ,EAAEC,OAAO,EAAE;EACzE,IAAID,QAAQ,IAAI,IAAI,EAAEA,QAAQ,GAAGR,aAAa,CAACO,OAAO,CAAC,IAAI,CAAC;EAC5D,IAAIW,aAAa,GAAGZ,oBAAoB,CAACC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,CAAC;EACpE,IAAII,MAAM,GAAGf,MAAM,CAACS,OAAO,EAAE,eAAe,EAAEU,OAAO,CAAC;EACtD,OAAO,YAAY;IACjBC,aAAa,CAAC,CAAC;IACfL,MAAM,CAAC,CAAC;EACV,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}