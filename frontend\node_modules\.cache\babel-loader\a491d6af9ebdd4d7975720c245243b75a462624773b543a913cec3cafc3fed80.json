{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarBrand = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-brand');\n  const Component = as || (props.href ? 'a' : 'span');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix)\n  });\n});\nNavbarBrand.displayName = 'NavbarBrand';\nexport default NavbarBrand;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "bsPrefix", "className", "as", "props", "ref", "Component", "href", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/NavbarBrand.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarBrand = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-brand');\n  const Component = as || (props.href ? 'a' : 'span');\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    className: classNames(className, bsPrefix)\n  });\n});\nNavbarBrand.displayName = 'NavbarBrand';\nexport default NavbarBrand;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,SAAS;EACTC,EAAE;EACF,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,cAAc,CAAC;EACvD,MAAMK,SAAS,GAAGH,EAAE,KAAKC,KAAK,CAACG,IAAI,GAAG,GAAG,GAAG,MAAM,CAAC;EACnD,OAAO,aAAaT,IAAI,CAACQ,SAAS,EAAE;IAClC,GAAGF,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRH,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAED,QAAQ;EAC3C,CAAC,CAAC;AACJ,CAAC,CAAC;AACFF,WAAW,CAACS,WAAW,GAAG,aAAa;AACvC,eAAeT,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}