{"ast": null, "code": "import ownerDocument from './ownerDocument';\n/**\n * Returns the actively focused element safely.\n *\n * @param doc the document to check\n */\n\nexport default function activeElement(doc) {\n  if (doc === void 0) {\n    doc = ownerDocument();\n  }\n\n  // Support: IE 9 only\n  // IE9 throws an \"Unspecified error\" accessing document.activeElement from an <iframe>\n  try {\n    var active = doc.activeElement; // IE11 returns a seemingly empty object in some cases when accessing\n    // document.activeElement from an <iframe>\n\n    if (!active || !active.nodeName) return null;\n    return active;\n  } catch (e) {\n    /* ie throws if no active element */\n    return doc.body;\n  }\n}", "map": {"version": 3, "names": ["ownerDocument", "activeElement", "doc", "active", "nodeName", "e", "body"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/dom-helpers/esm/activeElement.js"], "sourcesContent": ["import ownerDocument from './ownerDocument';\n/**\n * Returns the actively focused element safely.\n *\n * @param doc the document to check\n */\n\nexport default function activeElement(doc) {\n  if (doc === void 0) {\n    doc = ownerDocument();\n  }\n\n  // Support: IE 9 only\n  // IE9 throws an \"Unspecified error\" accessing document.activeElement from an <iframe>\n  try {\n    var active = doc.activeElement; // IE11 returns a seemingly empty object in some cases when accessing\n    // document.activeElement from an <iframe>\n\n    if (!active || !active.nodeName) return null;\n    return active;\n  } catch (e) {\n    /* ie throws if no active element */\n    return doc.body;\n  }\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C;AACA;AACA;AACA;AACA;;AAEA,eAAe,SAASC,aAAaA,CAACC,GAAG,EAAE;EACzC,IAAIA,GAAG,KAAK,KAAK,CAAC,EAAE;IAClBA,GAAG,GAAGF,aAAa,CAAC,CAAC;EACvB;;EAEA;EACA;EACA,IAAI;IACF,IAAIG,MAAM,GAAGD,GAAG,CAACD,aAAa,CAAC,CAAC;IAChC;;IAEA,IAAI,CAACE,MAAM,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE,OAAO,IAAI;IAC5C,OAAOD,MAAM;EACf,CAAC,CAAC,OAAOE,CAAC,EAAE;IACV;IACA,OAAOH,GAAG,CAACI,IAAI;EACjB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}