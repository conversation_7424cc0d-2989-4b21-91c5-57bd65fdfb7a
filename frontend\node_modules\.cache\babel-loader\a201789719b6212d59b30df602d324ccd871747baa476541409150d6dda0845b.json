{"ast": null, "code": "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "map": {"version": 3, "names": ["expandToHashMap", "value", "keys", "reduce", "hashMap", "key"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/@popperjs/core/lib/utils/expandToHashMap.js"], "sourcesContent": ["export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}"], "mappings": "AAAA,eAAe,SAASA,eAAeA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACnD,OAAOA,IAAI,CAACC,MAAM,CAAC,UAAUC,OAAO,EAAEC,GAAG,EAAE;IACzCD,OAAO,CAACC,GAAG,CAAC,GAAGJ,KAAK;IACpB,OAAOG,OAAO;EAChB,CAAC,EAAE,CAAC,CAAC,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}