from datetime import datetime
from database import db

class Employee(db.Model):
    __tablename__ = 'employees'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    department = db.Column(db.String(50), nullable=False)
    position = db.Column(db.String(50), nullable=False)
    hire_date = db.Column(db.Date, nullable=False)
    email = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.<PERSON>, default=True)
    
    # Relationships will be defined after all models are loaded
    
    def to_dict(self):
        """Convert employee object to dictionary"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'full_name': self.full_name,
            'department': self.department,
            'position': self.position,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'email': self.email,
            'phone': self.phone,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'is_active': self.is_active
        }
    
    def get_latest_evaluation(self):
        """Get the most recent evaluation for this employee"""
        return self.evaluations.filter_by(is_active=True).order_by(
            db.desc('evaluation_period_end')
        ).first()
    
    def get_evaluation_history(self, limit=10):
        """Get evaluation history for this employee"""
        return self.evaluations.order_by(
            db.desc('evaluation_period_end')
        ).limit(limit).all()
    
    def __repr__(self):
        return f'<Employee {self.employee_id}: {self.full_name}>'
