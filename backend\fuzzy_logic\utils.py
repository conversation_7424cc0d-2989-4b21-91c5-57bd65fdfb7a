"""
Utility functions for the Fuzzy Logic Engine
"""

import numpy as np
from typing import Dict, List, <PERSON><PERSON>

def calculate_attendance_score(present_days: int, total_work_days: int) -> float:
    """
    Calculate attendance score as percentage
    
    Args:
        present_days (int): Number of days employee was present
        total_work_days (int): Total number of work days in the period
    
    Returns:
        float: Attendance score (0-100)
    """
    if total_work_days == 0:
        return 0.0
    
    return min(100.0, (present_days / total_work_days) * 100)

def calculate_punctuality_score(present_days: int, late_arrivals: int) -> float:
    """
    Calculate punctuality score based on late arrivals
    
    Args:
        present_days (int): Number of days employee was present
        late_arrivals (int): Number of times employee arrived late
    
    Returns:
        float: Punctuality score (0-100)
    """
    if present_days == 0:
        return 100.0  # No attendance means no late arrivals
    
    # Calculate percentage of on-time arrivals
    on_time_percentage = max(0, (present_days - late_arrivals) / present_days) * 100
    return min(100.0, on_time_percentage)

def calculate_compliance_score(violations_count: int, evaluation_period_days: int) -> float:
    """
    Calculate compliance score based on violations
    
    Args:
        violations_count (int): Number of rule violations
        evaluation_period_days (int): Length of evaluation period in days
    
    Returns:
        float: Compliance score (0-100)
    """
    if evaluation_period_days == 0:
        return 100.0
    
    # Assume maximum acceptable violations is 1 per 30 days
    max_acceptable_violations = max(1, evaluation_period_days / 30)
    
    if violations_count == 0:
        return 100.0
    elif violations_count <= max_acceptable_violations:
        # Linear decrease from 100 to 70 for acceptable violations
        return 100 - (violations_count / max_acceptable_violations) * 30
    else:
        # Exponential decrease for excessive violations
        excess_violations = violations_count - max_acceptable_violations
        penalty = 30 + (excess_violations * 15)  # 15 points per excess violation
        return max(0, 70 - penalty)

def interpret_fuzzy_values(fuzzy_values: Dict) -> Dict[str, str]:
    """
    Interpret fuzzy membership values into human-readable descriptions
    
    Args:
        fuzzy_values (dict): Dictionary containing fuzzy membership values
    
    Returns:
        dict: Human-readable interpretations
    """
    interpretations = {}
    
    for criterion, values in fuzzy_values.items():
        max_membership = max(values.values())
        dominant_level = max(values, key=values.get)
        
        if max_membership >= 0.8:
            strength = "sangat"
        elif max_membership >= 0.6:
            strength = "cukup"
        elif max_membership >= 0.4:
            strength = "agak"
        else:
            strength = "sedikit"
        
        level_translations = {
            'low': 'rendah',
            'medium': 'sedang',
            'high': 'tinggi'
        }
        
        interpretations[criterion] = f"{strength} {level_translations.get(dominant_level, dominant_level)}"
    
    return interpretations

def get_improvement_suggestions(result: Dict) -> List[str]:
    """
    Generate improvement suggestions based on evaluation results
    
    Args:
        result (dict): Fuzzy logic evaluation result
    
    Returns:
        list: List of improvement suggestions
    """
    suggestions = []
    fuzzy_values = result['fuzzy_values']
    input_values = result['input_values']
    
    # Check attendance
    if input_values['attendance'] < 80:
        if fuzzy_values['attendance']['low'] > 0.5:
            suggestions.append("Tingkatkan kehadiran dengan mengurangi absensi tanpa keterangan")
        suggestions.append("Pertimbangkan untuk mengatur jadwal yang lebih fleksibel jika memungkinkan")
    
    # Check punctuality
    if input_values['punctuality'] < 80:
        if fuzzy_values['punctuality']['low'] > 0.5:
            suggestions.append("Perbaiki ketepatan waktu dengan datang lebih awal")
        suggestions.append("Evaluasi rute perjalanan dan waktu keberangkatan")
    
    # Check compliance
    if input_values['compliance'] < 80:
        if fuzzy_values['compliance']['low'] > 0.5:
            suggestions.append("Tingkatkan kepatuhan terhadap peraturan perusahaan")
        suggestions.append("Ikuti pelatihan tambahan mengenai kebijakan perusahaan")
    
    # General suggestions based on discipline level
    discipline_level = result['discipline_level']
    
    if discipline_level == 'Kurang':
        suggestions.extend([
            "Diperlukan perbaikan menyeluruh dalam semua aspek kedisiplinan",
            "Pertimbangkan untuk mengikuti program mentoring atau coaching",
            "Buat rencana perbaikan dengan target yang jelas dan terukur"
        ])
    elif discipline_level == 'Cukup':
        suggestions.extend([
            "Fokus pada peningkatan konsistensi dalam semua aspek",
            "Identifikasi area yang paling memerlukan perbaikan",
            "Tetapkan target peningkatan yang realistis"
        ])
    elif discipline_level == 'Disiplin':
        suggestions.extend([
            "Pertahankan kinerja yang baik dan tingkatkan konsistensi",
            "Jadilah contoh yang baik bagi rekan kerja lainnya"
        ])
    else:  # Sangat Disiplin
        suggestions.extend([
            "Pertahankan standar kedisiplinan yang sangat baik",
            "Berbagi pengalaman dan tips dengan rekan kerja lainnya",
            "Pertimbangkan untuk menjadi mentor bagi karyawan lain"
        ])
    
    return suggestions

def validate_input_scores(attendance: float, punctuality: float, compliance: float) -> Tuple[bool, str]:
    """
    Validate input scores for fuzzy logic evaluation
    
    Args:
        attendance (float): Attendance score
        punctuality (float): Punctuality score
        compliance (float): Compliance score
    
    Returns:
        tuple: (is_valid, error_message)
    """
    scores = {
        'attendance': attendance,
        'punctuality': punctuality,
        'compliance': compliance
    }
    
    for name, score in scores.items():
        if not isinstance(score, (int, float)):
            return False, f"{name} must be a number"
        
        if score < 0 or score > 100:
            return False, f"{name} must be between 0 and 100"
    
    return True, ""

def generate_evaluation_summary(result: Dict, employee_name: str = None) -> str:
    """
    Generate a text summary of the evaluation result
    
    Args:
        result (dict): Fuzzy logic evaluation result
        employee_name (str): Name of the employee (optional)
    
    Returns:
        str: Formatted evaluation summary
    """
    employee_text = f"untuk {employee_name} " if employee_name else ""
    
    summary = f"""
RINGKASAN EVALUASI KEDISIPLINAN {employee_text.upper()}

Nilai Input:
- Kehadiran: {result['input_values']['attendance']}%
- Ketepatan Waktu: {result['input_values']['punctuality']}%
- Kepatuhan: {result['input_values']['compliance']}%

Hasil Evaluasi:
- Skor Kedisiplinan: {result['discipline_score']}/100
- Tingkat Kedisiplinan: {result['discipline_level']}
- Tingkat Kepercayaan: {result['confidence_level']}%

Interpretasi Fuzzy:
"""
    
    interpretations = interpret_fuzzy_values(result['fuzzy_values'])
    for criterion, interpretation in interpretations.items():
        criterion_name = {
            'attendance': 'Kehadiran',
            'punctuality': 'Ketepatan Waktu',
            'compliance': 'Kepatuhan'
        }.get(criterion, criterion)
        summary += f"- {criterion_name}: {interpretation}\n"
    
    suggestions = get_improvement_suggestions(result)
    if suggestions:
        summary += "\nSaran Perbaikan:\n"
        for i, suggestion in enumerate(suggestions, 1):
            summary += f"{i}. {suggestion}\n"
    
    return summary.strip()

def export_evaluation_data(result: Dict, format: str = 'dict') -> Dict:
    """
    Export evaluation data in specified format
    
    Args:
        result (dict): Fuzzy logic evaluation result
        format (str): Export format ('dict', 'json', 'csv')
    
    Returns:
        dict: Formatted evaluation data
    """
    export_data = {
        'input_attendance': result['input_values']['attendance'],
        'input_punctuality': result['input_values']['punctuality'],
        'input_compliance': result['input_values']['compliance'],
        'discipline_score': result['discipline_score'],
        'discipline_level': result['discipline_level'],
        'confidence_level': result['confidence_level'],
        'attendance_fuzzy_low': result['fuzzy_values']['attendance']['low'],
        'attendance_fuzzy_medium': result['fuzzy_values']['attendance']['medium'],
        'attendance_fuzzy_high': result['fuzzy_values']['attendance']['high'],
        'punctuality_fuzzy_low': result['fuzzy_values']['punctuality']['low'],
        'punctuality_fuzzy_medium': result['fuzzy_values']['punctuality']['medium'],
        'punctuality_fuzzy_high': result['fuzzy_values']['punctuality']['high'],
        'compliance_fuzzy_low': result['fuzzy_values']['compliance']['low'],
        'compliance_fuzzy_medium': result['fuzzy_values']['compliance']['medium'],
        'compliance_fuzzy_high': result['fuzzy_values']['compliance']['high']
    }
    
    return export_data
