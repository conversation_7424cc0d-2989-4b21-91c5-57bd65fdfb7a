{"ast": null, "code": "'use client';\n\nimport t, { isValidElement as e, useRef as n, useLayoutEffect as o, useEffect as s, cloneElement as a, useReducer as r, useState as i, forwardRef as l } from \"react\";\nimport c from \"clsx\";\nconst u = t => \"number\" == typeof t && !isNaN(t),\n  d = t => \"string\" == typeof t,\n  p = t => \"function\" == typeof t,\n  m = t => d(t) || p(t) ? t : null,\n  f = t => e(t) || d(t) || p(t) || u(t);\nfunction g(t, e, n) {\n  void 0 === n && (n = 300);\n  const {\n    scrollHeight: o,\n    style: s\n  } = t;\n  requestAnimationFrame(() => {\n    s.minHeight = \"initial\", s.height = o + \"px\", s.transition = `all ${n}ms`, requestAnimationFrame(() => {\n      s.height = \"0\", s.padding = \"0\", s.margin = \"0\", setTimeout(e, n);\n    });\n  });\n}\nfunction h(e) {\n  let {\n    enter: a,\n    exit: r,\n    appendPosition: i = !1,\n    collapse: l = !0,\n    collapseDuration: c = 300\n  } = e;\n  return function (e) {\n    let {\n      children: u,\n      position: d,\n      preventExitTransition: p,\n      done: m,\n      nodeRef: f,\n      isIn: h\n    } = e;\n    const y = i ? `${a}--${d}` : a,\n      v = i ? `${r}--${d}` : r,\n      T = n(0);\n    return o(() => {\n      const t = f.current,\n        e = y.split(\" \"),\n        n = o => {\n          o.target === f.current && (t.dispatchEvent(new Event(\"d\")), t.removeEventListener(\"animationend\", n), t.removeEventListener(\"animationcancel\", n), 0 === T.current && \"animationcancel\" !== o.type && t.classList.remove(...e));\n        };\n      t.classList.add(...e), t.addEventListener(\"animationend\", n), t.addEventListener(\"animationcancel\", n);\n    }, []), s(() => {\n      const t = f.current,\n        e = () => {\n          t.removeEventListener(\"animationend\", e), l ? g(t, m, c) : m();\n        };\n      h || (p ? e() : (T.current = 1, t.className += ` ${v}`, t.addEventListener(\"animationend\", e)));\n    }, [h]), t.createElement(t.Fragment, null, u);\n  };\n}\nfunction y(t, e) {\n  return null != t ? {\n    content: t.content,\n    containerId: t.props.containerId,\n    id: t.props.toastId,\n    theme: t.props.theme,\n    type: t.props.type,\n    data: t.props.data || {},\n    isLoading: t.props.isLoading,\n    icon: t.props.icon,\n    status: e\n  } : {};\n}\nconst v = {\n    list: new Map(),\n    emitQueue: new Map(),\n    on(t, e) {\n      return this.list.has(t) || this.list.set(t, []), this.list.get(t).push(e), this;\n    },\n    off(t, e) {\n      if (e) {\n        const n = this.list.get(t).filter(t => t !== e);\n        return this.list.set(t, n), this;\n      }\n      return this.list.delete(t), this;\n    },\n    cancelEmit(t) {\n      const e = this.emitQueue.get(t);\n      return e && (e.forEach(clearTimeout), this.emitQueue.delete(t)), this;\n    },\n    emit(t) {\n      this.list.has(t) && this.list.get(t).forEach(e => {\n        const n = setTimeout(() => {\n          e(...[].slice.call(arguments, 1));\n        }, 0);\n        this.emitQueue.has(t) || this.emitQueue.set(t, []), this.emitQueue.get(t).push(n);\n      });\n    }\n  },\n  T = e => {\n    let {\n      theme: n,\n      type: o,\n      ...s\n    } = e;\n    return t.createElement(\"svg\", {\n      viewBox: \"0 0 24 24\",\n      width: \"100%\",\n      height: \"100%\",\n      fill: \"colored\" === n ? \"currentColor\" : `var(--toastify-icon-color-${o})`,\n      ...s\n    });\n  },\n  E = {\n    info: function (e) {\n      return t.createElement(T, {\n        ...e\n      }, t.createElement(\"path\", {\n        d: \"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z\"\n      }));\n    },\n    warning: function (e) {\n      return t.createElement(T, {\n        ...e\n      }, t.createElement(\"path\", {\n        d: \"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z\"\n      }));\n    },\n    success: function (e) {\n      return t.createElement(T, {\n        ...e\n      }, t.createElement(\"path\", {\n        d: \"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z\"\n      }));\n    },\n    error: function (e) {\n      return t.createElement(T, {\n        ...e\n      }, t.createElement(\"path\", {\n        d: \"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z\"\n      }));\n    },\n    spinner: function () {\n      return t.createElement(\"div\", {\n        className: \"Toastify__spinner\"\n      });\n    }\n  };\nfunction C(t) {\n  const [, o] = r(t => t + 1, 0),\n    [l, c] = i([]),\n    g = n(null),\n    h = n(new Map()).current,\n    T = t => -1 !== l.indexOf(t),\n    C = n({\n      toastKey: 1,\n      displayedToast: 0,\n      count: 0,\n      queue: [],\n      props: t,\n      containerId: null,\n      isToastActive: T,\n      getToast: t => h.get(t)\n    }).current;\n  function b(t) {\n    let {\n      containerId: e\n    } = t;\n    const {\n      limit: n\n    } = C.props;\n    !n || e && C.containerId !== e || (C.count -= C.queue.length, C.queue = []);\n  }\n  function I(t) {\n    c(e => null == t ? [] : e.filter(e => e !== t));\n  }\n  function _() {\n    const {\n      toastContent: t,\n      toastProps: e,\n      staleId: n\n    } = C.queue.shift();\n    O(t, e, n);\n  }\n  function L(t, n) {\n    let {\n      delay: s,\n      staleId: r,\n      ...i\n    } = n;\n    if (!f(t) || function (t) {\n      return !g.current || C.props.enableMultiContainer && t.containerId !== C.props.containerId || h.has(t.toastId) && null == t.updateId;\n    }(i)) return;\n    const {\n        toastId: l,\n        updateId: c,\n        data: T\n      } = i,\n      {\n        props: b\n      } = C,\n      L = () => I(l),\n      N = null == c;\n    N && C.count++;\n    const M = {\n      ...b,\n      style: b.toastStyle,\n      key: C.toastKey++,\n      ...Object.fromEntries(Object.entries(i).filter(t => {\n        let [e, n] = t;\n        return null != n;\n      })),\n      toastId: l,\n      updateId: c,\n      data: T,\n      closeToast: L,\n      isIn: !1,\n      className: m(i.className || b.toastClassName),\n      bodyClassName: m(i.bodyClassName || b.bodyClassName),\n      progressClassName: m(i.progressClassName || b.progressClassName),\n      autoClose: !i.isLoading && (R = i.autoClose, w = b.autoClose, !1 === R || u(R) && R > 0 ? R : w),\n      deleteToast() {\n        const t = y(h.get(l), \"removed\");\n        h.delete(l), v.emit(4, t);\n        const e = C.queue.length;\n        if (C.count = null == l ? C.count - C.displayedToast : C.count - 1, C.count < 0 && (C.count = 0), e > 0) {\n          const t = null == l ? C.props.limit : 1;\n          if (1 === e || 1 === t) C.displayedToast++, _();else {\n            const n = t > e ? e : t;\n            C.displayedToast = n;\n            for (let t = 0; t < n; t++) _();\n          }\n        } else o();\n      }\n    };\n    var R, w;\n    M.iconOut = function (t) {\n      let {\n          theme: n,\n          type: o,\n          isLoading: s,\n          icon: r\n        } = t,\n        i = null;\n      const l = {\n        theme: n,\n        type: o\n      };\n      return !1 === r || (p(r) ? i = r(l) : e(r) ? i = a(r, l) : d(r) || u(r) ? i = r : s ? i = E.spinner() : (t => t in E)(o) && (i = E[o](l))), i;\n    }(M), p(i.onOpen) && (M.onOpen = i.onOpen), p(i.onClose) && (M.onClose = i.onClose), M.closeButton = b.closeButton, !1 === i.closeButton || f(i.closeButton) ? M.closeButton = i.closeButton : !0 === i.closeButton && (M.closeButton = !f(b.closeButton) || b.closeButton);\n    let x = t;\n    e(t) && !d(t.type) ? x = a(t, {\n      closeToast: L,\n      toastProps: M,\n      data: T\n    }) : p(t) && (x = t({\n      closeToast: L,\n      toastProps: M,\n      data: T\n    })), b.limit && b.limit > 0 && C.count > b.limit && N ? C.queue.push({\n      toastContent: x,\n      toastProps: M,\n      staleId: r\n    }) : u(s) ? setTimeout(() => {\n      O(x, M, r);\n    }, s) : O(x, M, r);\n  }\n  function O(t, e, n) {\n    const {\n      toastId: o\n    } = e;\n    n && h.delete(n);\n    const s = {\n      content: t,\n      props: e\n    };\n    h.set(o, s), c(t => [...t, o].filter(t => t !== n)), v.emit(4, y(s, null == s.props.updateId ? \"added\" : \"updated\"));\n  }\n  return s(() => (C.containerId = t.containerId, v.cancelEmit(3).on(0, L).on(1, t => g.current && I(t)).on(5, b).emit(2, C), () => {\n    h.clear(), v.emit(3, C);\n  }), []), s(() => {\n    C.props = t, C.isToastActive = T, C.displayedToast = l.length;\n  }), {\n    getToastToRender: function (e) {\n      const n = new Map(),\n        o = Array.from(h.values());\n      return t.newestOnTop && o.reverse(), o.forEach(t => {\n        const {\n          position: e\n        } = t.props;\n        n.has(e) || n.set(e, []), n.get(e).push(t);\n      }), Array.from(n, t => e(t[0], t[1]));\n    },\n    containerRef: g,\n    isToastActive: T\n  };\n}\nfunction b(t) {\n  return t.targetTouches && t.targetTouches.length >= 1 ? t.targetTouches[0].clientX : t.clientX;\n}\nfunction I(t) {\n  return t.targetTouches && t.targetTouches.length >= 1 ? t.targetTouches[0].clientY : t.clientY;\n}\nfunction _(t) {\n  const [o, a] = i(!1),\n    [r, l] = i(!1),\n    c = n(null),\n    u = n({\n      start: 0,\n      x: 0,\n      y: 0,\n      delta: 0,\n      removalDistance: 0,\n      canCloseOnClick: !0,\n      canDrag: !1,\n      boundingRect: null,\n      didMove: !1\n    }).current,\n    d = n(t),\n    {\n      autoClose: m,\n      pauseOnHover: f,\n      closeToast: g,\n      onClick: h,\n      closeOnClick: y\n    } = t;\n  function v(e) {\n    if (t.draggable) {\n      \"touchstart\" === e.nativeEvent.type && e.nativeEvent.preventDefault(), u.didMove = !1, document.addEventListener(\"mousemove\", _), document.addEventListener(\"mouseup\", L), document.addEventListener(\"touchmove\", _), document.addEventListener(\"touchend\", L);\n      const n = c.current;\n      u.canCloseOnClick = !0, u.canDrag = !0, u.boundingRect = n.getBoundingClientRect(), n.style.transition = \"\", u.x = b(e.nativeEvent), u.y = I(e.nativeEvent), \"x\" === t.draggableDirection ? (u.start = u.x, u.removalDistance = n.offsetWidth * (t.draggablePercent / 100)) : (u.start = u.y, u.removalDistance = n.offsetHeight * (80 === t.draggablePercent ? 1.5 * t.draggablePercent : t.draggablePercent / 100));\n    }\n  }\n  function T(e) {\n    if (u.boundingRect) {\n      const {\n        top: n,\n        bottom: o,\n        left: s,\n        right: a\n      } = u.boundingRect;\n      \"touchend\" !== e.nativeEvent.type && t.pauseOnHover && u.x >= s && u.x <= a && u.y >= n && u.y <= o ? C() : E();\n    }\n  }\n  function E() {\n    a(!0);\n  }\n  function C() {\n    a(!1);\n  }\n  function _(e) {\n    const n = c.current;\n    u.canDrag && n && (u.didMove = !0, o && C(), u.x = b(e), u.y = I(e), u.delta = \"x\" === t.draggableDirection ? u.x - u.start : u.y - u.start, u.start !== u.x && (u.canCloseOnClick = !1), n.style.transform = `translate${t.draggableDirection}(${u.delta}px)`, n.style.opacity = \"\" + (1 - Math.abs(u.delta / u.removalDistance)));\n  }\n  function L() {\n    document.removeEventListener(\"mousemove\", _), document.removeEventListener(\"mouseup\", L), document.removeEventListener(\"touchmove\", _), document.removeEventListener(\"touchend\", L);\n    const e = c.current;\n    if (u.canDrag && u.didMove && e) {\n      if (u.canDrag = !1, Math.abs(u.delta) > u.removalDistance) return l(!0), void t.closeToast();\n      e.style.transition = \"transform 0.2s, opacity 0.2s\", e.style.transform = `translate${t.draggableDirection}(0)`, e.style.opacity = \"1\";\n    }\n  }\n  s(() => {\n    d.current = t;\n  }), s(() => (c.current && c.current.addEventListener(\"d\", E, {\n    once: !0\n  }), p(t.onOpen) && t.onOpen(e(t.children) && t.children.props), () => {\n    const t = d.current;\n    p(t.onClose) && t.onClose(e(t.children) && t.children.props);\n  }), []), s(() => (t.pauseOnFocusLoss && (document.hasFocus() || C(), window.addEventListener(\"focus\", E), window.addEventListener(\"blur\", C)), () => {\n    t.pauseOnFocusLoss && (window.removeEventListener(\"focus\", E), window.removeEventListener(\"blur\", C));\n  }), [t.pauseOnFocusLoss]);\n  const O = {\n    onMouseDown: v,\n    onTouchStart: v,\n    onMouseUp: T,\n    onTouchEnd: T\n  };\n  return m && f && (O.onMouseEnter = C, O.onMouseLeave = E), y && (O.onClick = t => {\n    h && h(t), u.canCloseOnClick && g();\n  }), {\n    playToast: E,\n    pauseToast: C,\n    isRunning: o,\n    preventExitTransition: r,\n    toastRef: c,\n    eventHandlers: O\n  };\n}\nfunction L(e) {\n  let {\n    closeToast: n,\n    theme: o,\n    ariaLabel: s = \"close\"\n  } = e;\n  return t.createElement(\"button\", {\n    className: `Toastify__close-button Toastify__close-button--${o}`,\n    type: \"button\",\n    onClick: t => {\n      t.stopPropagation(), n(t);\n    },\n    \"aria-label\": s\n  }, t.createElement(\"svg\", {\n    \"aria-hidden\": \"true\",\n    viewBox: \"0 0 14 16\"\n  }, t.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z\"\n  })));\n}\nfunction O(e) {\n  let {\n    delay: n,\n    isRunning: o,\n    closeToast: s,\n    type: a = \"default\",\n    hide: r,\n    className: i,\n    style: l,\n    controlledProgress: u,\n    progress: d,\n    rtl: m,\n    isIn: f,\n    theme: g\n  } = e;\n  const h = r || u && 0 === d,\n    y = {\n      ...l,\n      animationDuration: `${n}ms`,\n      animationPlayState: o ? \"running\" : \"paused\",\n      opacity: h ? 0 : 1\n    };\n  u && (y.transform = `scaleX(${d})`);\n  const v = c(\"Toastify__progress-bar\", u ? \"Toastify__progress-bar--controlled\" : \"Toastify__progress-bar--animated\", `Toastify__progress-bar-theme--${g}`, `Toastify__progress-bar--${a}`, {\n      \"Toastify__progress-bar--rtl\": m\n    }),\n    T = p(i) ? i({\n      rtl: m,\n      type: a,\n      defaultClassName: v\n    }) : c(v, i);\n  return t.createElement(\"div\", {\n    role: \"progressbar\",\n    \"aria-hidden\": h ? \"true\" : \"false\",\n    \"aria-label\": \"notification timer\",\n    className: T,\n    style: y,\n    [u && d >= 1 ? \"onTransitionEnd\" : \"onAnimationEnd\"]: u && d < 1 ? null : () => {\n      f && s();\n    }\n  });\n}\nconst N = n => {\n    const {\n        isRunning: o,\n        preventExitTransition: s,\n        toastRef: r,\n        eventHandlers: i\n      } = _(n),\n      {\n        closeButton: l,\n        children: u,\n        autoClose: d,\n        onClick: m,\n        type: f,\n        hideProgressBar: g,\n        closeToast: h,\n        transition: y,\n        position: v,\n        className: T,\n        style: E,\n        bodyClassName: C,\n        bodyStyle: b,\n        progressClassName: I,\n        progressStyle: N,\n        updateId: M,\n        role: R,\n        progress: w,\n        rtl: x,\n        toastId: $,\n        deleteToast: k,\n        isIn: P,\n        isLoading: B,\n        iconOut: D,\n        closeOnClick: A,\n        theme: z\n      } = n,\n      F = c(\"Toastify__toast\", `Toastify__toast-theme--${z}`, `Toastify__toast--${f}`, {\n        \"Toastify__toast--rtl\": x\n      }, {\n        \"Toastify__toast--close-on-click\": A\n      }),\n      H = p(T) ? T({\n        rtl: x,\n        position: v,\n        type: f,\n        defaultClassName: F\n      }) : c(F, T),\n      S = !!w || !d,\n      q = {\n        closeToast: h,\n        type: f,\n        theme: z\n      };\n    let Q = null;\n    return !1 === l || (Q = p(l) ? l(q) : e(l) ? a(l, q) : L(q)), t.createElement(y, {\n      isIn: P,\n      done: k,\n      position: v,\n      preventExitTransition: s,\n      nodeRef: r\n    }, t.createElement(\"div\", {\n      id: $,\n      onClick: m,\n      className: H,\n      ...i,\n      style: E,\n      ref: r\n    }, t.createElement(\"div\", {\n      ...(P && {\n        role: R\n      }),\n      className: p(C) ? C({\n        type: f\n      }) : c(\"Toastify__toast-body\", C),\n      style: b\n    }, null != D && t.createElement(\"div\", {\n      className: c(\"Toastify__toast-icon\", {\n        \"Toastify--animate-icon Toastify__zoom-enter\": !B\n      })\n    }, D), t.createElement(\"div\", null, u)), Q, t.createElement(O, {\n      ...(M && !S ? {\n        key: `pb-${M}`\n      } : {}),\n      rtl: x,\n      theme: z,\n      delay: d,\n      isRunning: o,\n      isIn: P,\n      closeToast: h,\n      hide: g,\n      type: f,\n      style: N,\n      className: I,\n      controlledProgress: S,\n      progress: w || 0\n    })));\n  },\n  M = function (t, e) {\n    return void 0 === e && (e = !1), {\n      enter: `Toastify--animate Toastify__${t}-enter`,\n      exit: `Toastify--animate Toastify__${t}-exit`,\n      appendPosition: e\n    };\n  },\n  R = h(M(\"bounce\", !0)),\n  w = h(M(\"slide\", !0)),\n  x = h(M(\"zoom\")),\n  $ = h(M(\"flip\")),\n  k = l((e, n) => {\n    const {\n        getToastToRender: o,\n        containerRef: a,\n        isToastActive: r\n      } = C(e),\n      {\n        className: i,\n        style: l,\n        rtl: u,\n        containerId: d\n      } = e;\n    function f(t) {\n      const e = c(\"Toastify__toast-container\", `Toastify__toast-container--${t}`, {\n        \"Toastify__toast-container--rtl\": u\n      });\n      return p(i) ? i({\n        position: t,\n        rtl: u,\n        defaultClassName: e\n      }) : c(e, m(i));\n    }\n    return s(() => {\n      n && (n.current = a.current);\n    }, []), t.createElement(\"div\", {\n      ref: a,\n      className: \"Toastify\",\n      id: d\n    }, o((e, n) => {\n      const o = n.length ? {\n        ...l\n      } : {\n        ...l,\n        pointerEvents: \"none\"\n      };\n      return t.createElement(\"div\", {\n        className: f(e),\n        style: o,\n        key: `container-${e}`\n      }, n.map((e, o) => {\n        let {\n          content: s,\n          props: a\n        } = e;\n        return t.createElement(N, {\n          ...a,\n          isIn: r(a.toastId),\n          style: {\n            ...a.style,\n            \"--nth\": o + 1,\n            \"--len\": n.length\n          },\n          key: `toast-${a.key}`\n        }, s);\n      }));\n    }));\n  });\nk.displayName = \"ToastContainer\", k.defaultProps = {\n  position: \"top-right\",\n  transition: R,\n  autoClose: 5e3,\n  closeButton: L,\n  pauseOnHover: !0,\n  pauseOnFocusLoss: !0,\n  closeOnClick: !0,\n  draggable: !0,\n  draggablePercent: 80,\n  draggableDirection: \"x\",\n  role: \"alert\",\n  theme: \"light\"\n};\nlet P,\n  B = new Map(),\n  D = [],\n  A = 1;\nfunction z() {\n  return \"\" + A++;\n}\nfunction F(t) {\n  return t && (d(t.toastId) || u(t.toastId)) ? t.toastId : z();\n}\nfunction H(t, e) {\n  return B.size > 0 ? v.emit(0, t, e) : D.push({\n    content: t,\n    options: e\n  }), e.toastId;\n}\nfunction S(t, e) {\n  return {\n    ...e,\n    type: e && e.type || t,\n    toastId: F(e)\n  };\n}\nfunction q(t) {\n  return (e, n) => H(e, S(t, n));\n}\nfunction Q(t, e) {\n  return H(t, S(\"default\", e));\n}\nQ.loading = (t, e) => H(t, S(\"default\", {\n  isLoading: !0,\n  autoClose: !1,\n  closeOnClick: !1,\n  closeButton: !1,\n  draggable: !1,\n  ...e\n})), Q.promise = function (t, e, n) {\n  let o,\n    {\n      pending: s,\n      error: a,\n      success: r\n    } = e;\n  s && (o = d(s) ? Q.loading(s, n) : Q.loading(s.render, {\n    ...n,\n    ...s\n  }));\n  const i = {\n      isLoading: null,\n      autoClose: null,\n      closeOnClick: null,\n      closeButton: null,\n      draggable: null\n    },\n    l = (t, e, s) => {\n      if (null == e) return void Q.dismiss(o);\n      const a = {\n          type: t,\n          ...i,\n          ...n,\n          data: s\n        },\n        r = d(e) ? {\n          render: e\n        } : e;\n      return o ? Q.update(o, {\n        ...a,\n        ...r\n      }) : Q(r.render, {\n        ...a,\n        ...r\n      }), s;\n    },\n    c = p(t) ? t() : t;\n  return c.then(t => l(\"success\", r, t)).catch(t => l(\"error\", a, t)), c;\n}, Q.success = q(\"success\"), Q.info = q(\"info\"), Q.error = q(\"error\"), Q.warning = q(\"warning\"), Q.warn = Q.warning, Q.dark = (t, e) => H(t, S(\"default\", {\n  theme: \"dark\",\n  ...e\n})), Q.dismiss = t => {\n  B.size > 0 ? v.emit(1, t) : D = D.filter(e => null != t && e.options.toastId !== t);\n}, Q.clearWaitingQueue = function (t) {\n  return void 0 === t && (t = {}), v.emit(5, t);\n}, Q.isActive = t => {\n  let e = !1;\n  return B.forEach(n => {\n    n.isToastActive && n.isToastActive(t) && (e = !0);\n  }), e;\n}, Q.update = function (t, e) {\n  void 0 === e && (e = {}), setTimeout(() => {\n    const n = function (t, e) {\n      let {\n        containerId: n\n      } = e;\n      const o = B.get(n || P);\n      return o && o.getToast(t);\n    }(t, e);\n    if (n) {\n      const {\n          props: o,\n          content: s\n        } = n,\n        a = {\n          delay: 100,\n          ...o,\n          ...e,\n          toastId: e.toastId || t,\n          updateId: z()\n        };\n      a.toastId !== t && (a.staleId = t);\n      const r = a.render || s;\n      delete a.render, H(r, a);\n    }\n  }, 0);\n}, Q.done = t => {\n  Q.update(t, {\n    progress: 1\n  });\n}, Q.onChange = t => (v.on(4, t), () => {\n  v.off(4, t);\n}), Q.POSITION = {\n  TOP_LEFT: \"top-left\",\n  TOP_RIGHT: \"top-right\",\n  TOP_CENTER: \"top-center\",\n  BOTTOM_LEFT: \"bottom-left\",\n  BOTTOM_RIGHT: \"bottom-right\",\n  BOTTOM_CENTER: \"bottom-center\"\n}, Q.TYPE = {\n  INFO: \"info\",\n  SUCCESS: \"success\",\n  WARNING: \"warning\",\n  ERROR: \"error\",\n  DEFAULT: \"default\"\n}, v.on(2, t => {\n  P = t.containerId || t, B.set(P, t), D.forEach(t => {\n    v.emit(0, t.content, t.options);\n  }), D = [];\n}).on(3, t => {\n  B.delete(t.containerId || t), 0 === B.size && v.off(0).off(1).off(5);\n});\nexport { R as Bounce, $ as Flip, E as Icons, w as Slide, k as ToastContainer, x as Zoom, g as collapseToast, h as cssTransition, Q as toast, _ as useToast, C as useToastContainer };", "map": {"version": 3, "names": [], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\utils\\propValidator.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\utils\\collapseToast.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\utils\\cssTransition.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\utils\\mapper.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\core\\eventManager.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\components\\Icons.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\hooks\\useToastContainer.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\hooks\\useToast.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\components\\CloseButton.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\components\\ProgressBar.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\components\\Toast.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\components\\Transitions.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\components\\ToastContainer.tsx", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\core\\toast.ts", "C:\\Users\\<USER>\\OneDrive\\Desktop\\FUZYY LOGIC\\frontend\\node_modules\\react-toastify\\src\\utils\\constant.ts"], "sourcesContent": ["import { isValidElement } from 'react';\n\nexport const isNum = (v: any): v is Number =>\n  typeof v === 'number' && !isNaN(v);\n\nexport const isStr = (v: any): v is String => typeof v === 'string';\n\nexport const isFn = (v: any): v is Function => typeof v === 'function';\n\nexport const parseClassName = (v: any) => (isStr(v) || isFn(v) ? v : null);\n\nexport const getAutoCloseDelay = (\n  toastAutoClose?: false | number,\n  containerAutoClose?: false | number\n) =>\n  toastAutoClose === false || (isNum(toastAutoClose) && toastAutoClose > 0)\n    ? toastAutoClose\n    : containerAutoClose;\n\nexport const canBeRendered = <T>(content: T): boolean =>\n  isValidElement(content) || isStr(content) || isFn(content) || isNum(content);\n", "import { Default } from './constant';\n\n/**\n * Used to collapse toast after exit animation\n */\nexport function collapseToast(\n  node: HTMLElement,\n  done: () => void,\n  duration = Default.COLLAPSE_DURATION\n) {\n  const { scrollHeight, style } = node;\n\n  requestAnimationFrame(() => {\n    style.minHeight = 'initial';\n    style.height = scrollHeight + 'px';\n    style.transition = `all ${duration}ms`;\n\n    requestAnimationFrame(() => {\n      style.height = '0';\n      style.padding = '0';\n      style.margin = '0';\n      setTimeout(done, duration as number);\n    });\n  });\n}\n", "import React, { useEffect, useLayoutEffect, useRef } from 'react';\nimport { collapseToast } from './collapseToast';\nimport { Default, SyntheticEvent } from './constant';\n\nimport { ToastTransitionProps } from '../types';\n\nexport interface CSSTransitionProps {\n  /**\n   * Css class to apply when toast enter\n   */\n  enter: string;\n\n  /**\n   * Css class to apply when toast leave\n   */\n  exit: string;\n\n  /**\n   * Append current toast position to the classname.\n   * If multiple classes are provided, only the last one will get the position\n   * For instance `myclass--top-center`...\n   * `Default: false`\n   */\n  appendPosition?: boolean;\n\n  /**\n   * Collapse toast smoothly when exit animation end\n   * `Default: true`\n   */\n  collapse?: boolean;\n\n  /**\n   * Collapse transition duration\n   * `Default: 300`\n   */\n  collapseDuration?: number;\n}\n\nconst enum AnimationStep {\n  Enter,\n  Exit\n}\n\n/**\n * Css animation that just work.\n * You could use animate.css for instance\n *\n *\n * ```\n * cssTransition({\n *   enter: \"animate__animated animate__bounceIn\",\n *   exit: \"animate__animated animate__bounceOut\"\n * })\n * ```\n *\n */\nexport function cssTransition({\n  enter,\n  exit,\n  appendPosition = false,\n  collapse = true,\n  collapseDuration = Default.COLLAPSE_DURATION\n}: CSSTransitionProps) {\n  return function ToastTransition({\n    children,\n    position,\n    preventExitTransition,\n    done,\n    nodeRef,\n    isIn\n  }: ToastTransitionProps) {\n    const enterClassName = appendPosition ? `${enter}--${position}` : enter;\n    const exitClassName = appendPosition ? `${exit}--${position}` : exit;\n    const animationStep = useRef(AnimationStep.Enter);\n\n    useLayoutEffect(() => {\n      const node = nodeRef.current!;\n      const classToToken = enterClassName.split(' ');\n\n      const onEntered = (e: AnimationEvent) => {\n        if (e.target !== nodeRef.current) return;\n\n        node.dispatchEvent(new Event(SyntheticEvent.ENTRANCE_ANIMATION_END));\n        node.removeEventListener('animationend', onEntered);\n        node.removeEventListener('animationcancel', onEntered);\n        if (\n          animationStep.current === AnimationStep.Enter &&\n          e.type !== 'animationcancel'\n        ) {\n          node.classList.remove(...classToToken);\n        }\n      };\n\n      const onEnter = () => {\n        node.classList.add(...classToToken);\n        node.addEventListener('animationend', onEntered);\n        node.addEventListener('animationcancel', onEntered);\n      };\n\n      onEnter();\n    }, []);\n\n    useEffect(() => {\n      const node = nodeRef.current!;\n\n      const onExited = () => {\n        node.removeEventListener('animationend', onExited);\n        collapse ? collapseToast(node, done, collapseDuration) : done();\n      };\n\n      const onExit = () => {\n        animationStep.current = AnimationStep.Exit;\n        node.className += ` ${exitClassName}`;\n        node.addEventListener('animationend', onExited);\n      };\n\n      if (!isIn) preventExitTransition ? onExited() : onExit();\n    }, [isIn]);\n\n    return <>{children}</>;\n  };\n}\n", "import { Toast, ToastItem, ToastItemStatus } from '../types';\n\nexport function toToastItem(toast: Toast, status: ToastItemStatus): ToastItem {\n  return toast != null\n    ? {\n        content: toast.content,\n        containerId: toast.props.containerId,\n        id: toast.props.toastId,\n        theme: toast.props.theme,\n        type: toast.props.type,\n        data: toast.props.data || {},\n        isLoading: toast.props.isLoading,\n        icon: toast.props.icon,\n        status\n      }\n    : // monkey patch for now\n      ({} as ToastItem);\n}\n", "import {\n  Id,\n  ToastContent,\n  ClearWaitingQueueParams,\n  NotValidatedToastProps,\n  ToastItem\n} from '../types';\nimport { ContainerInstance } from '../hooks';\n\nexport const enum Event {\n  Show,\n  Clear,\n  DidMount,\n  WillUnmount,\n  Change,\n  ClearWaitingQueue\n}\n\ntype OnShowCallback = (\n  content: ToastContent,\n  options: NotValidatedToastProps\n) => void;\ntype OnClearCallback = (id?: Id) => void;\ntype OnClearWaitingQueue = (params: ClearWaitingQueueParams) => void;\ntype OnDidMountCallback = (containerInstance: ContainerInstance) => void;\ntype OnWillUnmountCallback = OnDidMountCallback;\n\nexport type OnChangeCallback = (toast: ToastItem) => void;\n\ntype Callback =\n  | OnShowCallback\n  | OnClearCallback\n  | OnClearWaitingQueue\n  | OnDidMountCallback\n  | OnWillUnmountCallback\n  | OnChangeCallback;\ntype TimeoutId = ReturnType<typeof setTimeout>;\n\nexport interface EventManager {\n  list: Map<Event, Callback[]>;\n  emitQueue: Map<Event, TimeoutId[]>;\n  on(event: Event.Show, callback: OnShowCallback): EventManager;\n  on(event: Event.Clear, callback: OnClearCallback): EventManager;\n  on(\n    event: Event.ClearWaitingQueue,\n    callback: OnClearWaitingQueue\n  ): EventManager;\n  on(event: Event.DidMount, callback: OnDidMountCallback): EventManager;\n  on(event: Event.WillUnmount, callback: OnWillUnmountCallback): EventManager;\n  on(event: Event.Change, callback: OnChangeCallback): EventManager;\n  off(event: Event, callback?: Callback): EventManager;\n  cancelEmit(event: Event): EventManager;\n  emit<TData>(\n    event: Event.Show,\n    content: React.ReactNode | ToastContent<TData>,\n    options: NotValidatedToastProps\n  ): void;\n  emit(event: Event.Clear, id?: string | number): void;\n  emit(event: Event.ClearWaitingQueue, params: ClearWaitingQueueParams): void;\n  emit(event: Event.DidMount, containerInstance: ContainerInstance): void;\n  emit(event: Event.WillUnmount, containerInstance: ContainerInstance): void;\n  emit(event: Event.Change, data: ToastItem): void;\n}\n\nexport const eventManager: EventManager = {\n  list: new Map(),\n  emitQueue: new Map(),\n\n  on(event: Event, callback: Callback) {\n    this.list.has(event) || this.list.set(event, []);\n    this.list.get(event)!.push(callback);\n    return this;\n  },\n\n  off(event, callback) {\n    if (callback) {\n      const cb = this.list.get(event)!.filter(cb => cb !== callback);\n      this.list.set(event, cb);\n      return this;\n    }\n    this.list.delete(event);\n    return this;\n  },\n\n  cancelEmit(event) {\n    const timers = this.emitQueue.get(event);\n    if (timers) {\n      timers.forEach(clearTimeout);\n      this.emitQueue.delete(event);\n    }\n\n    return this;\n  },\n\n  /**\n   * Enqueue the event at the end of the call stack\n   * Doing so let the user call toast as follow:\n   * toast('1')\n   * toast('2')\n   * toast('3')\n   * Without setTimemout the code above will not work\n   */\n  emit(event: Event, ...args: any[]) {\n    this.list.has(event) &&\n      this.list.get(event)!.forEach((callback: Callback) => {\n        const timer: TimeoutId = setTimeout(() => {\n          // @ts-ignore\n          callback(...args);\n        }, 0);\n\n        this.emitQueue.has(event) || this.emitQueue.set(event, []);\n        this.emitQueue.get(event)!.push(timer);\n      });\n  }\n};\n", "import React, { cloneElement, isValidElement } from 'react';\n\nimport { Theme, ToastProps, TypeOptions } from '../types';\nimport { Default, isFn, isNum, isStr } from '../utils';\n\n/**\n * Used when providing custom icon\n */\nexport interface IconProps {\n  theme: Theme;\n  type: TypeOptions;\n}\n\nexport type BuiltInIconProps = React.SVGProps<SVGSVGElement> & IconProps;\n\nconst Svg: React.FC<BuiltInIconProps> = ({ theme, type, ...rest }) => (\n  <svg\n    viewBox=\"0 0 24 24\"\n    width=\"100%\"\n    height=\"100%\"\n    fill={\n      theme === 'colored'\n        ? 'currentColor'\n        : `var(--toastify-icon-color-${type})`\n    }\n    {...rest}\n  />\n);\n\nfunction Warning(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M23.32 17.191L15.438 2.184C14.728.833 13.416 0 11.996 0c-1.42 0-2.733.833-3.443 2.184L.533 17.448a4.744 4.744 0 000 4.368C1.243 23.167 2.555 24 3.975 24h16.05C22.22 24 24 22.044 24 19.632c0-.904-.251-1.746-.68-2.44zm-9.622 1.46c0 1.033-.724 1.823-1.698 1.823s-1.698-.79-1.698-1.822v-.043c0-1.028.724-1.822 1.698-1.822s1.698.79 1.698 1.822v.043zm.039-12.285l-.84 8.06c-.057.581-.408.943-.897.943-.49 0-.84-.367-.896-.942l-.84-8.065c-.057-.624.25-1.095.779-1.095h1.91c.528.005.84.476.784 1.1z\" />\n    </Svg>\n  );\n}\n\nfunction Info(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M12 0a12 12 0 1012 12A12.013 12.013 0 0012 0zm.25 5a1.5 1.5 0 11-1.5 1.5 1.5 1.5 0 011.5-1.5zm2.25 13.5h-4a1 1 0 010-2h.75a.25.25 0 00.25-.25v-4.5a.25.25 0 00-.25-.25h-.75a1 1 0 010-2h1a2 2 0 012 2v4.75a.25.25 0 00.25.25h.75a1 1 0 110 2z\" />\n    </Svg>\n  );\n}\n\nfunction Success(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M12 0a12 12 0 1012 12A12.014 12.014 0 0012 0zm6.927 8.2l-6.845 9.289a1.011 1.011 0 01-1.43.188l-4.888-3.908a1 1 0 111.25-1.562l4.076 3.261 6.227-8.451a1 1 0 111.61 1.183z\" />\n    </Svg>\n  );\n}\n\nfunction Error(props: BuiltInIconProps) {\n  return (\n    <Svg {...props}>\n      <path d=\"M11.983 0a12.206 12.206 0 00-8.51 3.653A11.8 11.8 0 000 12.207 11.779 11.779 0 0011.8 24h.214A12.111 12.111 0 0024 11.791 11.766 11.766 0 0011.983 0zM10.5 16.542a1.476 1.476 0 011.449-1.53h.027a1.527 1.527 0 011.523 1.47 1.475 1.475 0 01-1.449 1.53h-.027a1.529 1.529 0 01-1.523-1.47zM11 12.5v-6a1 1 0 012 0v6a1 1 0 11-2 0z\" />\n    </Svg>\n  );\n}\n\nfunction Spinner() {\n  return <div className={`${Default.CSS_NAMESPACE}__spinner`} />;\n}\n\nexport const Icons = {\n  info: Info,\n  warning: Warning,\n  success: Success,\n  error: Error,\n  spinner: Spinner\n};\n\nconst maybeIcon = (type: string): type is keyof typeof Icons => type in Icons;\n\nexport function getIcon({ theme, type, isLoading, icon }: ToastProps) {\n  let Icon: React.ReactNode = null;\n  const iconProps = { theme, type };\n\n  if (icon === false) {\n    // hide\n  } else if (isFn(icon)) {\n    Icon = icon(iconProps);\n  } else if (isValidElement(icon)) {\n    Icon = cloneElement(icon, iconProps);\n  } else if (isStr(icon) || isNum(icon)) {\n    Icon = icon;\n  } else if (isLoading) {\n    Icon = Icons.spinner();\n  } else if (maybeIcon(type)) {\n    Icon = Icons[type](iconProps);\n  }\n\n  return Icon;\n}\n", "import {\n  useEffect,\n  useRef,\n  useReducer,\n  cloneElement,\n  isValidElement,\n  useState,\n  ReactElement\n} from 'react';\nimport {\n  parseClassName,\n  canBeRendered,\n  isFn,\n  isNum,\n  isStr,\n  getAutoCloseDelay,\n  toToastItem\n} from '../utils';\nimport { eventManager, Event } from '../core/eventManager';\n\nimport {\n  Id,\n  ToastContainerProps,\n  ToastProps,\n  ToastContent,\n  Toast,\n  ToastPosition,\n  ClearWaitingQueueParams,\n  NotValidatedToastProps\n} from '../types';\n\nimport { getIcon } from '../components/Icons';\n\ninterface QueuedToast {\n  toastContent: ToastContent;\n  toastProps: ToastProps;\n  staleId?: Id;\n}\n\nexport interface ContainerInstance {\n  toastKey: number;\n  displayedToast: number;\n  props: ToastContainerProps;\n  containerId?: Id | null;\n  isToastActive: (toastId: Id) => boolean;\n  getToast: (id: Id) => Toast | null | undefined;\n  queue: QueuedToast[];\n  count: number;\n}\n\nexport function useToastContainer(props: ToastContainerProps) {\n  const [, forceUpdate] = useReducer(x => x + 1, 0);\n  const [toastIds, setToastIds] = useState<Id[]>([]);\n  const containerRef = useRef(null);\n  const toastToRender = useRef(new Map<Id, Toast>()).current;\n  const isToastActive = (id: Id) => toastIds.indexOf(id) !== -1;\n  const instance = useRef<ContainerInstance>({\n    toastKey: 1,\n    displayedToast: 0,\n    count: 0,\n    queue: [],\n    props,\n    containerId: null,\n    isToastActive,\n    getToast: id => toastToRender.get(id)\n  }).current;\n\n  useEffect(() => {\n    instance.containerId = props.containerId;\n    eventManager\n      .cancelEmit(Event.WillUnmount)\n      .on(Event.Show, buildToast)\n      .on(Event.Clear, toastId => containerRef.current && removeToast(toastId))\n      .on(Event.ClearWaitingQueue, clearWaitingQueue)\n      .emit(Event.DidMount, instance);\n\n    return () => {\n      toastToRender.clear();\n      eventManager.emit(Event.WillUnmount, instance);\n    };\n  }, []);\n\n  useEffect(() => {\n    instance.props = props;\n    instance.isToastActive = isToastActive;\n    instance.displayedToast = toastIds.length;\n  });\n\n  function clearWaitingQueue({ containerId }: ClearWaitingQueueParams) {\n    const { limit } = instance.props;\n    if (limit && (!containerId || instance.containerId === containerId)) {\n      instance.count -= instance.queue.length;\n      instance.queue = [];\n    }\n  }\n\n  function removeToast(toastId?: Id) {\n    setToastIds(state =>\n      toastId == null ? [] : state.filter(id => id !== toastId)\n    );\n  }\n\n  function dequeueToast() {\n    const { toastContent, toastProps, staleId } =\n      instance.queue.shift() as QueuedToast;\n    appendToast(toastContent, toastProps, staleId);\n  }\n\n  /**\n   * check if a container is attached to the dom\n   * check for multi-container, build only if associated\n   * check for duplicate toastId if no update\n   */\n  function isNotValid(options: NotValidatedToastProps) {\n    return (\n      !containerRef.current ||\n      (instance.props.enableMultiContainer &&\n        options.containerId !== instance.props.containerId) ||\n      (toastToRender.has(options.toastId) && options.updateId == null)\n    );\n  }\n\n  // this function and all the function called inside needs to rely on refs\n  function buildToast(\n    content: ToastContent,\n    { delay, staleId, ...options }: NotValidatedToastProps\n  ) {\n    if (!canBeRendered(content) || isNotValid(options)) return;\n\n    const { toastId, updateId, data } = options;\n    const { props } = instance;\n    const closeToast = () => removeToast(toastId);\n    const isNotAnUpdate = updateId == null;\n\n    if (isNotAnUpdate) instance.count++;\n\n    const toastProps = {\n      ...props,\n      style: props.toastStyle,\n      key: instance.toastKey++,\n      ...Object.fromEntries(\n        Object.entries(options).filter(([_, v]) => v != null)\n      ),\n      toastId,\n      updateId,\n      data,\n      closeToast,\n      isIn: false,\n      className: parseClassName(options.className || props.toastClassName),\n      bodyClassName: parseClassName(\n        options.bodyClassName || props.bodyClassName\n      ),\n      progressClassName: parseClassName(\n        options.progressClassName || props.progressClassName\n      ),\n      autoClose: options.isLoading\n        ? false\n        : getAutoCloseDelay(options.autoClose, props.autoClose),\n      deleteToast() {\n        const removed = toToastItem(toastToRender.get(toastId)!, 'removed');\n        toastToRender.delete(toastId);\n\n        eventManager.emit(Event.Change, removed);\n\n        const queueLen = instance.queue.length;\n        instance.count =\n          toastId == null\n            ? instance.count - instance.displayedToast\n            : instance.count - 1;\n\n        if (instance.count < 0) instance.count = 0;\n\n        if (queueLen > 0) {\n          const freeSlot = toastId == null ? instance.props.limit! : 1;\n\n          if (queueLen === 1 || freeSlot === 1) {\n            instance.displayedToast++;\n            dequeueToast();\n          } else {\n            const toDequeue = freeSlot > queueLen ? queueLen : freeSlot;\n            instance.displayedToast = toDequeue;\n\n            for (let i = 0; i < toDequeue; i++) dequeueToast();\n          }\n        } else {\n          forceUpdate();\n        }\n      }\n    } as ToastProps;\n\n    toastProps.iconOut = getIcon(toastProps);\n\n    if (isFn(options.onOpen)) toastProps.onOpen = options.onOpen;\n    if (isFn(options.onClose)) toastProps.onClose = options.onClose;\n\n    toastProps.closeButton = props.closeButton;\n\n    if (options.closeButton === false || canBeRendered(options.closeButton)) {\n      toastProps.closeButton = options.closeButton;\n    } else if (options.closeButton === true) {\n      toastProps.closeButton = canBeRendered(props.closeButton)\n        ? props.closeButton\n        : true;\n    }\n\n    let toastContent = content;\n\n    if (isValidElement(content) && !isStr(content.type)) {\n      toastContent = cloneElement(content as ReactElement, {\n        closeToast,\n        toastProps,\n        data\n      });\n    } else if (isFn(content)) {\n      toastContent = content({ closeToast, toastProps, data });\n    }\n\n    // not handling limit + delay by design. Waiting for user feedback first\n    if (\n      props.limit &&\n      props.limit > 0 &&\n      instance.count > props.limit &&\n      isNotAnUpdate\n    ) {\n      instance.queue.push({ toastContent, toastProps, staleId });\n    } else if (isNum(delay)) {\n      setTimeout(() => {\n        appendToast(toastContent, toastProps, staleId);\n      }, delay);\n    } else {\n      appendToast(toastContent, toastProps, staleId);\n    }\n  }\n\n  function appendToast(\n    content: ToastContent,\n    toastProps: ToastProps,\n    staleId?: Id\n  ) {\n    const { toastId } = toastProps;\n\n    if (staleId) toastToRender.delete(staleId);\n\n    const toast = {\n      content,\n      props: toastProps\n    };\n    toastToRender.set(toastId, toast);\n\n    setToastIds(state => [...state, toastId].filter(id => id !== staleId));\n    eventManager.emit(\n      Event.Change,\n      toToastItem(toast, toast.props.updateId == null ? 'added' : 'updated')\n    );\n  }\n\n  function getToastToRender<T>(\n    cb: (position: ToastPosition, toastList: Toast[]) => T\n  ) {\n    const toRender = new Map<ToastPosition, Toast[]>();\n    const collection = Array.from(toastToRender.values());\n\n    if (props.newestOnTop) collection.reverse();\n\n    collection.forEach(toast => {\n      const { position } = toast.props;\n      toRender.has(position) || toRender.set(position, []);\n      toRender.get(position)!.push(toast);\n    });\n\n    return Array.from(toRender, p => cb(p[0], p[1]));\n  }\n\n  return {\n    getToastToRender,\n    containerRef,\n    isToastActive\n  };\n}\n", "import {\n  useState,\n  useRef,\n  useEffect,\n  isValidElement,\n  DOMAttributes\n} from 'react';\n\nimport { isFn, Default, Direction, SyntheticEvent } from '../utils';\nimport { ToastProps } from '../types';\n\ninterface Draggable {\n  start: number;\n  x: number;\n  y: number;\n  delta: number;\n  removalDistance: number;\n  canCloseOnClick: boolean;\n  canDrag: boolean;\n  boundingRect: DOMRect | null;\n  didMove: boolean;\n}\n\ntype DragEvent = MouseEvent & TouchEvent;\n\nfunction getX(e: DragEvent) {\n  return e.targetTouches && e.targetTouches.length >= 1\n    ? e.targetTouches[0].clientX\n    : e.clientX;\n}\n\nfunction getY(e: DragEvent) {\n  return e.targetTouches && e.targetTouches.length >= 1\n    ? e.targetTouches[0].clientY\n    : e.clientY;\n}\n\nexport function useToast(props: ToastProps) {\n  const [isRunning, setIsRunning] = useState(false);\n  const [preventExitTransition, setPreventExitTransition] = useState(false);\n  const toastRef = useRef<HTMLDivElement>(null);\n  const drag = useRef<Draggable>({\n    start: 0,\n    x: 0,\n    y: 0,\n    delta: 0,\n    removalDistance: 0,\n    canCloseOnClick: true,\n    canDrag: false,\n    boundingRect: null,\n    didMove: false\n  }).current;\n  const syncProps = useRef(props);\n  const { autoClose, pauseOnHover, closeToast, onClick, closeOnClick } = props;\n\n  useEffect(() => {\n    syncProps.current = props;\n  });\n\n  useEffect(() => {\n    if (toastRef.current)\n      toastRef.current.addEventListener(\n        SyntheticEvent.ENTRANCE_ANIMATION_END,\n        playToast,\n        { once: true }\n      );\n\n    if (isFn(props.onOpen))\n      props.onOpen(isValidElement(props.children) && props.children.props);\n\n    return () => {\n      const props = syncProps.current;\n      if (isFn(props.onClose))\n        props.onClose(isValidElement(props.children) && props.children.props);\n    };\n  }, []);\n\n  useEffect(() => {\n    props.pauseOnFocusLoss && bindFocusEvents();\n    return () => {\n      props.pauseOnFocusLoss && unbindFocusEvents();\n    };\n  }, [props.pauseOnFocusLoss]);\n\n  function onDragStart(\n    e: React.MouseEvent<HTMLElement, MouseEvent> | React.TouchEvent<HTMLElement>\n  ) {\n    if (props.draggable) {\n      // required for ios safari to prevent default swipe behavior\n      if (e.nativeEvent.type === 'touchstart') e.nativeEvent.preventDefault();\n\n      bindDragEvents();\n      const toast = toastRef.current!;\n      drag.canCloseOnClick = true;\n      drag.canDrag = true;\n      drag.boundingRect = toast.getBoundingClientRect();\n      toast.style.transition = '';\n      drag.x = getX(e.nativeEvent as DragEvent);\n      drag.y = getY(e.nativeEvent as DragEvent);\n\n      if (props.draggableDirection === Direction.X) {\n        drag.start = drag.x;\n        drag.removalDistance =\n          toast.offsetWidth * (props.draggablePercent / 100);\n      } else {\n        drag.start = drag.y;\n        drag.removalDistance =\n          toast.offsetHeight *\n          (props.draggablePercent === Default.DRAGGABLE_PERCENT\n            ? props.draggablePercent * 1.5\n            : props.draggablePercent / 100);\n      }\n    }\n  }\n\n  function onDragTransitionEnd(\n    e: React.MouseEvent<HTMLElement, MouseEvent> | React.TouchEvent<HTMLElement>\n  ) {\n    if (drag.boundingRect) {\n      const { top, bottom, left, right } = drag.boundingRect;\n\n      if (\n        e.nativeEvent.type !== 'touchend' &&\n        props.pauseOnHover &&\n        drag.x >= left &&\n        drag.x <= right &&\n        drag.y >= top &&\n        drag.y <= bottom\n      ) {\n        pauseToast();\n      } else {\n        playToast();\n      }\n    }\n  }\n\n  function playToast() {\n    setIsRunning(true);\n  }\n\n  function pauseToast() {\n    setIsRunning(false);\n  }\n\n  function bindFocusEvents() {\n    if (!document.hasFocus()) pauseToast();\n\n    window.addEventListener('focus', playToast);\n    window.addEventListener('blur', pauseToast);\n  }\n\n  function unbindFocusEvents() {\n    window.removeEventListener('focus', playToast);\n    window.removeEventListener('blur', pauseToast);\n  }\n\n  function bindDragEvents() {\n    drag.didMove = false;\n    document.addEventListener('mousemove', onDragMove);\n    document.addEventListener('mouseup', onDragEnd);\n\n    document.addEventListener('touchmove', onDragMove);\n    document.addEventListener('touchend', onDragEnd);\n  }\n\n  function unbindDragEvents() {\n    document.removeEventListener('mousemove', onDragMove);\n    document.removeEventListener('mouseup', onDragEnd);\n\n    document.removeEventListener('touchmove', onDragMove);\n    document.removeEventListener('touchend', onDragEnd);\n  }\n\n  function onDragMove(e: MouseEvent | TouchEvent) {\n    const toast = toastRef.current!;\n    if (drag.canDrag && toast) {\n      drag.didMove = true;\n      if (isRunning) pauseToast();\n      drag.x = getX(e as DragEvent);\n      drag.y = getY(e as DragEvent);\n      if (props.draggableDirection === Direction.X) {\n        drag.delta = drag.x - drag.start;\n      } else {\n        drag.delta = drag.y - drag.start;\n      }\n\n      // prevent false positif during a toast click\n      if (drag.start !== drag.x) drag.canCloseOnClick = false;\n      toast.style.transform = `translate${props.draggableDirection}(${drag.delta}px)`;\n      toast.style.opacity = `${\n        1 - Math.abs(drag.delta / drag.removalDistance)\n      }`;\n    }\n  }\n\n  function onDragEnd() {\n    unbindDragEvents();\n    const toast = toastRef.current!;\n    if (drag.canDrag && drag.didMove && toast) {\n      drag.canDrag = false;\n      if (Math.abs(drag.delta) > drag.removalDistance) {\n        setPreventExitTransition(true);\n        props.closeToast();\n        return;\n      }\n      toast.style.transition = 'transform 0.2s, opacity 0.2s';\n      toast.style.transform = `translate${props.draggableDirection}(0)`;\n      toast.style.opacity = '1';\n    }\n  }\n\n  const eventHandlers: DOMAttributes<HTMLElement> = {\n    onMouseDown: onDragStart,\n    onTouchStart: onDragStart,\n    onMouseUp: onDragTransitionEnd,\n    onTouchEnd: onDragTransitionEnd\n  };\n\n  if (autoClose && pauseOnHover) {\n    eventHandlers.onMouseEnter = pauseToast;\n    eventHandlers.onMouseLeave = playToast;\n  }\n\n  // prevent toast from closing when user drags the toast\n  if (closeOnClick) {\n    eventHandlers.onClick = (e: React.MouseEvent) => {\n      onClick && onClick(e);\n      drag.canCloseOnClick && closeToast();\n    };\n  }\n\n  return {\n    playToast,\n    pauseToast,\n    isRunning,\n    preventExitTransition,\n    toastRef,\n    eventHandlers\n  };\n}\n", "import React from 'react';\nimport { Default } from '../utils';\nimport { Theme, TypeOptions } from '../types';\n\nexport interface CloseButtonProps {\n  closeToast: (e: React.MouseEvent<HTMLElement>) => void;\n  type: TypeOptions;\n  ariaLabel?: string;\n  theme: Theme;\n}\n\nexport function CloseButton({\n  closeToast,\n  theme,\n  ariaLabel = 'close'\n}: CloseButtonProps) {\n  return (\n    <button\n      className={`${Default.CSS_NAMESPACE}__close-button ${Default.CSS_NAMESPACE}__close-button--${theme}`}\n      type=\"button\"\n      onClick={e => {\n        e.stopPropagation();\n        closeToast(e);\n      }}\n      aria-label={ariaLabel}\n    >\n      <svg aria-hidden=\"true\" viewBox=\"0 0 14 16\">\n        <path\n          fillRule=\"evenodd\"\n          d=\"M7.71 8.23l3.75 3.75-1.48 1.48-3.75-3.75-3.75 3.75L1 11.98l3.75-3.75L1 4.48 2.48 3l3.75 3.75L9.98 3l1.48 1.48-3.75 3.75z\"\n        />\n      </svg>\n    </button>\n  );\n}\n", "import React from 'react';\nimport cx from 'clsx';\n\nimport { Default, isFn, Type } from './../utils';\nimport { TypeOptions, ToastClassName, Theme } from '../types';\n\nexport interface ProgressBarProps {\n  /**\n   * The animation delay which determine when to close the toast\n   */\n  delay: number;\n\n  /**\n   * Whether or not the animation is running or paused\n   */\n  isRunning: boolean;\n\n  /**\n   * Func to close the current toast\n   */\n  closeToast: () => void;\n\n  /**\n   * Optional type : info, success ...\n   */\n  type?: TypeOptions;\n\n  /**\n   * The theme that is currently used\n   */\n  theme: Theme;\n\n  /**\n   * Hide or not the progress bar\n   */\n  hide?: boolean;\n\n  /**\n   * Optionnal className\n   */\n  className?: ToastClassName;\n\n  /**\n   * Optionnal inline style\n   */\n  style?: React.CSSProperties;\n\n  /**\n   * Tell wether or not controlled progress bar is used\n   */\n  controlledProgress?: boolean;\n\n  /**\n   * Controlled progress value\n   */\n  progress?: number | string;\n\n  /**\n   * Support rtl content\n   */\n  rtl?: boolean;\n\n  /**\n   * Tell if the component is visible on screen or not\n   */\n  isIn?: boolean;\n}\n\nexport function ProgressBar({\n  delay,\n  isRunning,\n  closeToast,\n  type = Type.DEFAULT,\n  hide,\n  className,\n  style: userStyle,\n  controlledProgress,\n  progress,\n  rtl,\n  isIn,\n  theme\n}: ProgressBarProps) {\n  const isHidden = hide || (controlledProgress && progress === 0);\n  const style: React.CSSProperties = {\n    ...userStyle,\n    animationDuration: `${delay}ms`,\n    animationPlayState: isRunning ? 'running' : 'paused',\n    opacity: isHidden ? 0 : 1\n  };\n\n  if (controlledProgress) style.transform = `scaleX(${progress})`;\n  const defaultClassName = cx(\n    `${Default.CSS_NAMESPACE}__progress-bar`,\n    controlledProgress\n      ? `${Default.CSS_NAMESPACE}__progress-bar--controlled`\n      : `${Default.CSS_NAMESPACE}__progress-bar--animated`,\n    `${Default.CSS_NAMESPACE}__progress-bar-theme--${theme}`,\n    `${Default.CSS_NAMESPACE}__progress-bar--${type}`,\n    {\n      [`${Default.CSS_NAMESPACE}__progress-bar--rtl`]: rtl\n    }\n  );\n  const classNames = isFn(className)\n    ? className({\n        rtl,\n        type,\n        defaultClassName\n      })\n    : cx(defaultClassName, className);\n\n  // 🧐 controlledProgress is derived from progress\n  // so if controlledProgress is set\n  // it means that this is also the case for progress\n  const animationEvent = {\n    [controlledProgress && (progress as number)! >= 1\n      ? 'onTransitionEnd'\n      : 'onAnimationEnd']:\n      controlledProgress && (progress as number)! < 1\n        ? null\n        : () => {\n            isIn && closeToast();\n          }\n  };\n\n  // TODO: add aria-valuenow, aria-valuemax, aria-valuemin\n\n  return (\n    <div\n      role=\"progressbar\"\n      aria-hidden={isHidden ? 'true' : 'false'}\n      aria-label=\"notification timer\"\n      className={classNames}\n      style={style}\n      {...animationEvent}\n    />\n  );\n}\n", "import React, { cloneElement, isValidElement, ReactNode } from 'react';\nimport cx from 'clsx';\n\nimport { ProgressBar } from './ProgressBar';\nimport { CloseButton } from './CloseButton';\nimport { ToastProps } from '../types';\nimport { Default, isFn } from '../utils';\nimport { useToast } from '../hooks/useToast';\n\nexport const Toast: React.FC<ToastProps> = props => {\n  const { isRunning, preventExitTransition, toastRef, eventHandlers } =\n    useToast(props);\n  const {\n    closeButton,\n    children,\n    autoClose,\n    onClick,\n    type,\n    hideProgressBar,\n    closeToast,\n    transition: Transition,\n    position,\n    className,\n    style,\n    bodyClassName,\n    bodyStyle,\n    progressClassName,\n    progressStyle,\n    updateId,\n    role,\n    progress,\n    rtl,\n    toastId,\n    deleteToast,\n    isIn,\n    isLoading,\n    iconOut,\n    closeOnClick,\n    theme\n  } = props;\n  const defaultClassName = cx(\n    `${Default.CSS_NAMESPACE}__toast`,\n    `${Default.CSS_NAMESPACE}__toast-theme--${theme}`,\n    `${Default.CSS_NAMESPACE}__toast--${type}`,\n    {\n      [`${Default.CSS_NAMESPACE}__toast--rtl`]: rtl\n    },\n    {\n      [`${Default.CSS_NAMESPACE}__toast--close-on-click`]: closeOnClick\n    }\n  );\n  const cssClasses = isFn(className)\n    ? className({\n        rtl,\n        position,\n        type,\n        defaultClassName\n      })\n    : cx(defaultClassName, className);\n  const isProgressControlled = !!progress || !autoClose;\n\n  const closeButtonProps = { closeToast, type, theme };\n  let Close: React.ReactNode = null;\n\n  if (closeButton === false) {\n    // hide\n  } else if (isFn(closeButton)) {\n    Close = closeButton(closeButtonProps);\n  } else if (isValidElement(closeButton)) {\n    Close = cloneElement(closeButton, closeButtonProps);\n  } else {\n    Close = CloseButton(closeButtonProps);\n  }\n\n  return (\n    <Transition\n      isIn={isIn}\n      done={deleteToast}\n      position={position}\n      preventExitTransition={preventExitTransition}\n      nodeRef={toastRef}\n    >\n      <div\n        id={toastId as string}\n        onClick={onClick}\n        className={cssClasses}\n        {...eventHandlers}\n        style={style}\n        ref={toastRef}\n      >\n        <div\n          {...(isIn && { role: role })}\n          className={\n            isFn(bodyClassName)\n              ? bodyClassName({ type })\n              : cx(`${Default.CSS_NAMESPACE}__toast-body`, bodyClassName)\n          }\n          style={bodyStyle}\n        >\n          {iconOut != null && (\n            <div\n              className={cx(`${Default.CSS_NAMESPACE}__toast-icon`, {\n                [`${Default.CSS_NAMESPACE}--animate-icon ${Default.CSS_NAMESPACE}__zoom-enter`]:\n                  !isLoading\n              })}\n            >\n              {iconOut}\n            </div>\n          )}\n          <div>{children as ReactNode}</div>\n        </div>\n        {Close}\n        <ProgressBar\n          {...(updateId && !isProgressControlled\n            ? { key: `pb-${updateId}` }\n            : {})}\n          rtl={rtl}\n          theme={theme}\n          delay={autoClose as number}\n          isRunning={isRunning}\n          isIn={isIn}\n          closeToast={closeToast}\n          hide={hideProgressBar}\n          type={type}\n          style={progressStyle}\n          className={progressClassName}\n          controlledProgress={isProgressControlled}\n          progress={progress || 0}\n        />\n      </div>\n    </Transition>\n  );\n};\n", "import { Default, cssTransition } from '../utils';\n\nconst getConfig = (animationName: string, appendPosition = false) => ({\n  enter: `${Default.CSS_NAMESPACE}--animate ${Default.CSS_NAMESPACE}__${animationName}-enter`,\n  exit: `${Default.CSS_NAMESPACE}--animate ${Default.CSS_NAMESPACE}__${animationName}-exit`,\n  appendPosition\n});\n\nconst Bounce = cssTransition(getConfig('bounce', true));\n\nconst Slide = cssTransition(getConfig('slide', true));\n\nconst Zoom = cssTransition(getConfig('zoom'));\n\nconst Flip = cssTransition(getConfig('flip'));\n\nexport { Bounce, Slide, Zoom, Flip };\n", "// https://github.com/yannickcr/eslint-plugin-react/issues/3140\n/* eslint react/prop-types: \"off\" */\nimport React, { forwardRef, StyleHTMLAttributes, useEffect } from 'react';\nimport cx from 'clsx';\n\nimport { Toast } from './Toast';\nimport { CloseButton } from './CloseButton';\nimport { Bounce } from './Transitions';\nimport { Direction, Default, parseClassName, isFn } from '../utils';\nimport { useToastContainer } from '../hooks/useToastContainer';\nimport { ToastContainerProps, ToastPosition } from '../types';\n\nexport const ToastContainer = forwardRef<HTMLDivElement, ToastContainerProps>(\n  (props, ref) => {\n    const { getToastToRender, containerRef, isToastActive } =\n      useToastContainer(props);\n    const { className, style, rtl, containerId } = props;\n\n    function getClassName(position: ToastPosition) {\n      const defaultClassName = cx(\n        `${Default.CSS_NAMESPACE}__toast-container`,\n        `${Default.CSS_NAMESPACE}__toast-container--${position}`,\n        { [`${Default.CSS_NAMESPACE}__toast-container--rtl`]: rtl }\n      );\n      return isFn(className)\n        ? className({\n            position,\n            rtl,\n            defaultClassName\n          })\n        : cx(defaultClassName, parseClassName(className));\n    }\n\n    useEffect(() => {\n      if (ref) {\n        (ref as React.MutableRefObject<HTMLDivElement>).current =\n          containerRef.current!;\n      }\n    }, []);\n\n    return (\n      <div\n        ref={containerRef}\n        className={Default.CSS_NAMESPACE as string}\n        id={containerId as string}\n      >\n        {getToastToRender((position, toastList) => {\n          const containerStyle: React.CSSProperties = !toastList.length\n            ? { ...style, pointerEvents: 'none' }\n            : { ...style };\n\n          return (\n            <div\n              className={getClassName(position)}\n              style={containerStyle}\n              key={`container-${position}`}\n            >\n              {toastList.map(({ content, props: toastProps }, i) => {\n                return (\n                  <Toast\n                    {...toastProps}\n                    isIn={isToastActive(toastProps.toastId)}\n                    style={\n                      {\n                        ...toastProps.style,\n                        '--nth': i + 1,\n                        '--len': toastList.length\n                      } as StyleHTMLAttributes<HTMLDivElement>\n                    }\n                    key={`toast-${toastProps.key}`}\n                  >\n                    {content}\n                  </Toast>\n                );\n              })}\n            </div>\n          );\n        })}\n      </div>\n    );\n  }\n);\n\nToastContainer.displayName = 'ToastContainer';\n\nToastContainer.defaultProps = {\n  position: 'top-right',\n  transition: Bounce,\n  autoClose: 5000,\n  closeButton: CloseButton,\n  pauseOnHover: true,\n  pauseOnFocusLoss: true,\n  closeOnClick: true,\n  draggable: true,\n  draggablePercent: Default.DRAGGABLE_PERCENT as number,\n  draggableDirection: Direction.X,\n  role: 'alert',\n  theme: 'light'\n};\n", "import { P<PERSON><PERSON><PERSON>, TYPE, isStr, isNum, isFn, Type } from '../utils';\nimport { eventManager, OnChangeCallback, Event } from './eventManager';\nimport {\n  ToastContent,\n  ToastOptions,\n  ToastProps,\n  Id,\n  UpdateOptions,\n  ClearWaitingQueueParams,\n  NotValidatedToastProps,\n  TypeOptions\n} from '../types';\nimport { ContainerInstance } from '../hooks';\n\ninterface EnqueuedToast {\n  content: ToastContent<any>;\n  options: NotValidatedToastProps;\n}\n\nlet containers = new Map<ContainerInstance | Id, ContainerInstance>();\nlet latestInstance: ContainerInstance | Id;\nlet queue: EnqueuedToast[] = [];\nlet TOAST_ID = 1;\n\n/**\n * Get the toast by id, given it's in the DOM, otherwise returns null\n */\nfunction getToast(toastId: Id, { containerId }: ToastOptions) {\n  const container = containers.get(containerId || latestInstance);\n  return container && container.getToast(toastId);\n}\n\n/**\n * Generate a random toastId\n */\nfunction generateToastId() {\n  return `${TOAST_ID++}`;\n}\n\n/**\n * Generate a toastId or use the one provided\n */\nfunction getToastId(options?: ToastOptions) {\n  return options && (isStr(options.toastId) || isNum(options.toastId))\n    ? options.toastId\n    : generateToastId();\n}\n\n/**\n * If the container is not mounted, the toast is enqueued and\n * the container lazy mounted\n */\nfunction dispatchToast<TData>(\n  content: ToastContent<TData>,\n  options: NotValidatedToastProps\n): Id {\n  if (containers.size > 0) {\n    eventManager.emit(Event.Show, content, options);\n  } else {\n    queue.push({ content, options });\n  }\n\n  return options.toastId;\n}\n\n/**\n * Merge provided options with the defaults settings and generate the toastId\n */\nfunction mergeOptions(type: string, options?: ToastOptions) {\n  return {\n    ...options,\n    type: (options && options.type) || type,\n    toastId: getToastId(options)\n  } as NotValidatedToastProps;\n}\n\nfunction createToastByType(type: string) {\n  return <TData = unknown>(\n    content: ToastContent<TData>,\n    options?: ToastOptions\n  ) => dispatchToast(content, mergeOptions(type, options));\n}\n\nfunction toast<TData = unknown>(\n  content: ToastContent<TData>,\n  options?: ToastOptions\n) {\n  return dispatchToast(content, mergeOptions(Type.DEFAULT, options));\n}\n\ntoast.loading = <TData = unknown>(\n  content: ToastContent<TData>,\n  options?: ToastOptions\n) =>\n  dispatchToast(\n    content,\n    mergeOptions(Type.DEFAULT, {\n      isLoading: true,\n      autoClose: false,\n      closeOnClick: false,\n      closeButton: false,\n      draggable: false,\n      ...options\n    })\n  );\n\nexport interface ToastPromiseParams<\n  TData = unknown,\n  TError = unknown,\n  TPending = unknown\n> {\n  pending?: string | UpdateOptions<TPending>;\n  success?: string | UpdateOptions<TData>;\n  error?: string | UpdateOptions<TError>;\n}\n\nfunction handlePromise<TData = unknown, TError = unknown, TPending = unknown>(\n  promise: Promise<TData> | (() => Promise<TData>),\n  { pending, error, success }: ToastPromiseParams<TData, TError, TPending>,\n  options?: ToastOptions\n) {\n  let id: Id;\n\n  if (pending) {\n    id = isStr(pending)\n      ? toast.loading(pending, options)\n      : toast.loading(pending.render, {\n          ...options,\n          ...(pending as ToastOptions)\n        });\n  }\n\n  const resetParams = {\n    isLoading: null,\n    autoClose: null,\n    closeOnClick: null,\n    closeButton: null,\n    draggable: null\n  };\n\n  const resolver = <T>(\n    type: TypeOptions,\n    input: string | UpdateOptions<T> | undefined,\n    result: T\n  ) => {\n    // Remove the toast if the input has not been provided. This prevents the toast from hanging\n    // in the pending state if a success/error toast has not been provided.\n    if (input == null) {\n      toast.dismiss(id);\n      return;\n    }\n\n    const baseParams = {\n      type,\n      ...resetParams,\n      ...options,\n      data: result\n    };\n    const params = isStr(input) ? { render: input } : input;\n\n    // if the id is set we know that it's an update\n    if (id) {\n      toast.update(id, {\n        ...baseParams,\n        ...params\n      } as UpdateOptions);\n    } else {\n      // using toast.promise without loading\n      toast(params!.render, {\n        ...baseParams,\n        ...params\n      } as ToastOptions);\n    }\n\n    return result;\n  };\n\n  const p = isFn(promise) ? promise() : promise;\n\n  //call the resolvers only when needed\n  p.then(result => resolver('success', success, result)).catch(err =>\n    resolver('error', error, err)\n  );\n\n  return p;\n}\n\ntoast.promise = handlePromise;\ntoast.success = createToastByType(Type.SUCCESS);\ntoast.info = createToastByType(Type.INFO);\ntoast.error = createToastByType(Type.ERROR);\ntoast.warning = createToastByType(Type.WARNING);\ntoast.warn = toast.warning;\ntoast.dark = (content: ToastContent, options?: ToastOptions) =>\n  dispatchToast(\n    content,\n    mergeOptions(Type.DEFAULT, {\n      theme: 'dark',\n      ...options\n    })\n  );\n\n/**\n * Remove toast programmaticaly\n */\ntoast.dismiss = (id?: Id) => {\n  if (containers.size > 0) {\n    eventManager.emit(Event.Clear, id);\n  } else {\n    queue = queue.filter(t => id != null && t.options.toastId !== id);\n  }\n};\n\n/**\n * Clear waiting queue when limit is used\n */\ntoast.clearWaitingQueue = (params: ClearWaitingQueueParams = {}) =>\n  eventManager.emit(Event.ClearWaitingQueue, params);\n\n/**\n * return true if one container is displaying the toast\n */\ntoast.isActive = (id: Id) => {\n  let isToastActive = false;\n\n  containers.forEach(container => {\n    if (container.isToastActive && container.isToastActive(id)) {\n      isToastActive = true;\n    }\n  });\n\n  return isToastActive;\n};\n\ntoast.update = <TData = unknown>(\n  toastId: Id,\n  options: UpdateOptions<TData> = {}\n) => {\n  setTimeout(() => {\n    const toast = getToast(toastId, options as ToastOptions);\n    if (toast) {\n      const { props: oldOptions, content: oldContent } = toast;\n\n      const nextOptions = {\n        delay: 100,\n        ...oldOptions,\n        ...options,\n        toastId: options.toastId || toastId,\n        updateId: generateToastId()\n      } as ToastProps & UpdateOptions;\n\n      if (nextOptions.toastId !== toastId) nextOptions.staleId = toastId;\n\n      const content = nextOptions.render || oldContent;\n      delete nextOptions.render;\n\n      dispatchToast(content, nextOptions);\n    }\n  }, 0);\n};\n\n/**\n * Used for controlled progress bar.\n */\ntoast.done = (id: Id) => {\n  toast.update(id, {\n    progress: 1\n  });\n};\n\n/**\n * Subscribe to change when a toast is added, removed and updated\n *\n * Usage:\n * ```\n * const unsubscribe = toast.onChange((payload) => {\n *   switch (payload.status) {\n *   case \"added\":\n *     // new toast added\n *     break;\n *   case \"updated\":\n *     // toast updated\n *     break;\n *   case \"removed\":\n *     // toast has been removed\n *     break;\n *   }\n * })\n * ```\n */\ntoast.onChange = (callback: OnChangeCallback) => {\n  eventManager.on(Event.Change, callback);\n  return () => {\n    eventManager.off(Event.Change, callback);\n  };\n};\n\n/**\n * @deprecated\n * Will be removed in the next major release.\n */\ntoast.POSITION = POSITION;\n\n/**\n * @deprecated\n * Will be removed in the next major release.\n */\ntoast.TYPE = TYPE;\n\n/**\n * Wait until the ToastContainer is mounted to dispatch the toast\n * and attach isActive method\n */\neventManager\n  .on(Event.DidMount, (containerInstance: ContainerInstance) => {\n    latestInstance = containerInstance.containerId || containerInstance;\n    containers.set(latestInstance, containerInstance);\n\n    queue.forEach(item => {\n      eventManager.emit(Event.Show, item.content, item.options);\n    });\n\n    queue = [];\n  })\n  .on(Event.WillUnmount, (containerInstance: ContainerInstance) => {\n    containers.delete(containerInstance.containerId || containerInstance);\n\n    if (containers.size === 0) {\n      eventManager\n        .off(Event.Show)\n        .off(Event.Clear)\n        .off(Event.ClearWaitingQueue);\n    }\n  });\n\nexport { toast };\n", "import { ToastPosition, TypeOptions } from '../types';\n\ntype KeyOfPosition =\n  | 'TOP_LEFT'\n  | 'TOP_RIGHT'\n  | 'TOP_CENTER'\n  | 'BOTTOM_LEFT'\n  | 'BOTTOM_RIGHT'\n  | 'BOTTOM_CENTER';\n\ntype KeyOfType = 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR' | 'DEFAULT';\n\n/**\n * @deprecated\n */\nexport const POSITION: { [key in KeyOfPosition]: ToastPosition } = {\n  TOP_LEFT: 'top-left',\n  TOP_RIGHT: 'top-right',\n  TOP_CENTER: 'top-center',\n  BOTTOM_LEFT: 'bottom-left',\n  BOTTOM_RIGHT: 'bottom-right',\n  BOTTOM_CENTER: 'bottom-center'\n};\n\n/**\n * @deprecated\n */\nexport const TYPE: { [key in KeyOfType]: TypeOptions } = {\n  INFO: 'info',\n  SUCCESS: 'success',\n  WARNING: 'warning',\n  ERROR: 'error',\n  DEFAULT: 'default'\n};\n\nexport const enum Type {\n  INFO = 'info',\n  SUCCESS = 'success',\n  WARNING = 'warning',\n  ERROR = 'error',\n  DEFAULT = 'default'\n}\n\nexport const enum Default {\n  COLLAPSE_DURATION = 300,\n  DEBOUNCE_DURATION = 50,\n  CSS_NAMESPACE = 'Toastify',\n  DRAGGABLE_PERCENT = 80\n}\n\nexport const enum Direction {\n  X = 'x',\n  Y = 'y'\n}\n\nexport const enum SyntheticEvent {\n  ENTRANCE_ANIMATION_END = 'd'\n}\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}