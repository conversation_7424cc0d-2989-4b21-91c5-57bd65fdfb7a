{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionBody from './AccordionBody';\nimport AccordionButton from './AccordionButton';\nimport AccordionCollapse from './AccordionCollapse';\nimport AccordionContext from './AccordionContext';\nimport AccordionHeader from './AccordionHeader';\nimport AccordionItem from './AccordionItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Accordion = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    activeKey,\n    bsPrefix,\n    className,\n    onSelect,\n    flush,\n    alwaysOpen,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'accordion');\n  const contextValue = useMemo(() => ({\n    activeEventKey: activeKey,\n    onSelect,\n    alwaysOpen\n  }), [activeKey, onSelect, alwaysOpen]);\n  return /*#__PURE__*/_jsx(AccordionContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...controlledProps,\n      className: classNames(className, prefix, flush && `${prefix}-flush`)\n    })\n  });\n});\nAccordion.displayName = 'Accordion';\nexport default Object.assign(Accordion, {\n  Button: AccordionButton,\n  Collapse: AccordionCollapse,\n  Item: AccordionItem,\n  Header: AccordionHeader,\n  Body: AccordionBody\n});", "map": {"version": 3, "names": ["classNames", "React", "useMemo", "useUncontrolled", "useBootstrapPrefix", "AccordionBody", "Accordion<PERSON><PERSON><PERSON>", "AccordionCollapse", "AccordionContext", "Accordi<PERSON><PERSON><PERSON><PERSON>", "AccordionItem", "jsx", "_jsx", "Accordion", "forwardRef", "props", "ref", "as", "Component", "active<PERSON><PERSON>", "bsPrefix", "className", "onSelect", "flush", "alwaysOpen", "controlledProps", "prefix", "contextValue", "activeEventKey", "Provider", "value", "children", "displayName", "Object", "assign", "<PERSON><PERSON>", "Collapse", "<PERSON><PERSON>", "Header", "Body"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/Accordion.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useUncontrolled } from 'uncontrollable';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport AccordionBody from './AccordionBody';\nimport AccordionButton from './AccordionButton';\nimport AccordionCollapse from './AccordionCollapse';\nimport AccordionContext from './AccordionContext';\nimport AccordionHeader from './AccordionHeader';\nimport AccordionItem from './AccordionItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Accordion = /*#__PURE__*/React.forwardRef((props, ref) => {\n  const {\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    activeKey,\n    bsPrefix,\n    className,\n    onSelect,\n    flush,\n    alwaysOpen,\n    ...controlledProps\n  } = useUncontrolled(props, {\n    activeKey: 'onSelect'\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'accordion');\n  const contextValue = useMemo(() => ({\n    activeEventKey: activeKey,\n    onSelect,\n    alwaysOpen\n  }), [activeKey, onSelect, alwaysOpen]);\n  return /*#__PURE__*/_jsx(AccordionContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...controlledProps,\n      className: classNames(className, prefix, flush && `${prefix}-flush`)\n    })\n  });\n});\nAccordion.displayName = 'Accordion';\nexport default Object.assign(Accordion, {\n  Button: AccordionButton,\n  Collapse: AccordionCollapse,\n  Item: AccordionItem,\n  Header: AccordionHeader,\n  Body: AccordionBody\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,SAAS,GAAG,aAAaZ,KAAK,CAACa,UAAU,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;EAC9D,MAAM;IACJ;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrBC,SAAS;IACTC,QAAQ;IACRC,SAAS;IACTC,QAAQ;IACRC,KAAK;IACLC,UAAU;IACV,GAAGC;EACL,CAAC,GAAGtB,eAAe,CAACY,KAAK,EAAE;IACzBI,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAMO,MAAM,GAAGtB,kBAAkB,CAACgB,QAAQ,EAAE,WAAW,CAAC;EACxD,MAAMO,YAAY,GAAGzB,OAAO,CAAC,OAAO;IAClC0B,cAAc,EAAET,SAAS;IACzBG,QAAQ;IACRE;EACF,CAAC,CAAC,EAAE,CAACL,SAAS,EAAEG,QAAQ,EAAEE,UAAU,CAAC,CAAC;EACtC,OAAO,aAAaZ,IAAI,CAACJ,gBAAgB,CAACqB,QAAQ,EAAE;IAClDC,KAAK,EAAEH,YAAY;IACnBI,QAAQ,EAAE,aAAanB,IAAI,CAACM,SAAS,EAAE;MACrCF,GAAG,EAAEA,GAAG;MACR,GAAGS,eAAe;MAClBJ,SAAS,EAAErB,UAAU,CAACqB,SAAS,EAAEK,MAAM,EAAEH,KAAK,IAAI,GAAGG,MAAM,QAAQ;IACrE,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFb,SAAS,CAACmB,WAAW,GAAG,WAAW;AACnC,eAAeC,MAAM,CAACC,MAAM,CAACrB,SAAS,EAAE;EACtCsB,MAAM,EAAE7B,eAAe;EACvB8B,QAAQ,EAAE7B,iBAAiB;EAC3B8B,IAAI,EAAE3B,aAAa;EACnB4B,MAAM,EAAE7B,eAAe;EACvB8B,IAAI,EAAElC;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}