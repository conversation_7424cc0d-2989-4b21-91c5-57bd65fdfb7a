{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\FUZYY LOGIC\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Container, Row, Col, Card, Form, Button, Alert, Spinner } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const {\n    login\n  } = useAuth();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n    try {\n      const result = await login(formData.username, formData.password);\n      if (!result.success) {\n        setError(result.error);\n      }\n    } catch (err) {\n      setError('Terjadi kesalahan saat login');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Container, {\n    fluid: true,\n    className: \"min-vh-100 d-flex align-items-center justify-content-center bg-light\",\n    children: /*#__PURE__*/_jsxDEV(Row, {\n      className: \"w-100\",\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        md: 6,\n        lg: 4,\n        className: \"mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"shadow\",\n          children: /*#__PURE__*/_jsxDEV(Card.Body, {\n            className: \"p-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-primary mb-2\",\n                children: \"Fuzzy Logic\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-muted\",\n                children: \"Employee Discipline System\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-muted\",\n                children: \"Sistem Evaluasi Kedisiplinan Karyawan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n              variant: \"danger\",\n              className: \"mb-3\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Form, {\n              onSubmit: handleSubmit,\n              children: [/*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Username\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"text\",\n                  name: \"username\",\n                  value: formData.username,\n                  onChange: handleChange,\n                  placeholder: \"Masukkan username\",\n                  required: true,\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 60,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Form.Group, {\n                className: \"mb-4\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Label, {\n                  children: \"Password\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 72,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Control, {\n                  type: \"password\",\n                  name: \"password\",\n                  value: formData.password,\n                  onChange: handleChange,\n                  placeholder: \"Masukkan password\",\n                  required: true,\n                  disabled: loading\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 73,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"primary\",\n                type: \"submit\",\n                className: \"w-100 mb-3\",\n                disabled: loading,\n                size: \"lg\",\n                children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [/*#__PURE__*/_jsxDEV(Spinner, {\n                    as: \"span\",\n                    animation: \"border\",\n                    size: \"sm\",\n                    role: \"status\",\n                    \"aria-hidden\": \"true\",\n                    className: \"me-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 23\n                  }, this), \"Logging in...\"]\n                }, void 0, true) : 'Login'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center mt-4\",\n              children: /*#__PURE__*/_jsxDEV(\"small\", {\n                className: \"text-muted\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Demo Credentials:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 53\n                }, this), \"Admin: admin / admin123\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 42\n                }, this), \"HRD: hrd / hrd123\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 110,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 40,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"V8EChQpZGka8lH1CFSAPe3ELRQE=\", false, function () {\n  return [useAuth];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Container", "Row", "Col", "Card", "Form", "<PERSON><PERSON>", "<PERSON><PERSON>", "Spinner", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "formData", "setFormData", "username", "password", "loading", "setLoading", "error", "setError", "login", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "result", "success", "err", "fluid", "className", "children", "md", "lg", "Body", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onSubmit", "Group", "Label", "Control", "type", "onChange", "placeholder", "required", "disabled", "size", "as", "animation", "role", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Con<PERSON><PERSON>, <PERSON>, <PERSON>, Card, Form, But<PERSON>, Alert, Spinner } from 'react-bootstrap';\nimport { useAuth } from '../contexts/AuthContext';\n\nfunction Login() {\n  const [formData, setFormData] = useState({\n    username: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  \n  const { login } = useAuth();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const result = await login(formData.username, formData.password);\n      if (!result.success) {\n        setError(result.error);\n      }\n    } catch (err) {\n      setError('<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat login');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Container fluid className=\"min-vh-100 d-flex align-items-center justify-content-center bg-light\">\n      <Row className=\"w-100\">\n        <Col md={6} lg={4} className=\"mx-auto\">\n          <Card className=\"shadow\">\n            <Card.Body className=\"p-5\">\n              <div className=\"text-center mb-4\">\n                <h2 className=\"text-primary mb-2\">Fuzzy Logic</h2>\n                <h4 className=\"text-muted\">Employee Discipline System</h4>\n                <p className=\"text-muted\">Sistem Evaluasi Kedisiplinan Karyawan</p>\n              </div>\n\n              {error && (\n                <Alert variant=\"danger\" className=\"mb-3\">\n                  {error}\n                </Alert>\n              )}\n\n              <Form onSubmit={handleSubmit}>\n                <Form.Group className=\"mb-3\">\n                  <Form.Label>Username</Form.Label>\n                  <Form.Control\n                    type=\"text\"\n                    name=\"username\"\n                    value={formData.username}\n                    onChange={handleChange}\n                    placeholder=\"Masukkan username\"\n                    required\n                    disabled={loading}\n                  />\n                </Form.Group>\n\n                <Form.Group className=\"mb-4\">\n                  <Form.Label>Password</Form.Label>\n                  <Form.Control\n                    type=\"password\"\n                    name=\"password\"\n                    value={formData.password}\n                    onChange={handleChange}\n                    placeholder=\"Masukkan password\"\n                    required\n                    disabled={loading}\n                  />\n                </Form.Group>\n\n                <Button\n                  variant=\"primary\"\n                  type=\"submit\"\n                  className=\"w-100 mb-3\"\n                  disabled={loading}\n                  size=\"lg\"\n                >\n                  {loading ? (\n                    <>\n                      <Spinner\n                        as=\"span\"\n                        animation=\"border\"\n                        size=\"sm\"\n                        role=\"status\"\n                        aria-hidden=\"true\"\n                        className=\"me-2\"\n                      />\n                      Logging in...\n                    </>\n                  ) : (\n                    'Login'\n                  )}\n                </Button>\n              </Form>\n\n              <div className=\"text-center mt-4\">\n                <small className=\"text-muted\">\n                  <strong>Demo Credentials:</strong><br />\n                  Admin: admin / admin123<br />\n                  HRD: hrd / hrd123\n                </small>\n              </div>\n            </Card.Body>\n          </Card>\n        </Col>\n      </Row>\n    </Container>\n  );\n}\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,QAAQ,iBAAiB;AACzF,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAElD,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAM;IAAEwB;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAE3B,MAAMgB,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,QAAQ,CAAC,EAAE,CAAC;IACZF,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF,MAAMW,MAAM,GAAG,MAAMR,KAAK,CAACR,QAAQ,CAACE,QAAQ,EAAEF,QAAQ,CAACG,QAAQ,CAAC;MAChE,IAAI,CAACa,MAAM,CAACC,OAAO,EAAE;QACnBV,QAAQ,CAACS,MAAM,CAACV,KAAK,CAAC;MACxB;IACF,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZX,QAAQ,CAAC,8BAA8B,CAAC;IAC1C,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA,CAACV,SAAS;IAACkC,KAAK;IAACC,SAAS,EAAC,sEAAsE;IAAAC,QAAA,eAC/F1B,OAAA,CAACT,GAAG;MAACkC,SAAS,EAAC,OAAO;MAAAC,QAAA,eACpB1B,OAAA,CAACR,GAAG;QAACmC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAACH,SAAS,EAAC,SAAS;QAAAC,QAAA,eACpC1B,OAAA,CAACP,IAAI;UAACgC,SAAS,EAAC,QAAQ;UAAAC,QAAA,eACtB1B,OAAA,CAACP,IAAI,CAACoC,IAAI;YAACJ,SAAS,EAAC,KAAK;YAAAC,QAAA,gBACxB1B,OAAA;cAAKyB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B1B,OAAA;gBAAIyB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAW;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDjC,OAAA;gBAAIyB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAA0B;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1DjC,OAAA;gBAAGyB,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAC;cAAqC;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC,EAELtB,KAAK,iBACJX,OAAA,CAACJ,KAAK;cAACsC,OAAO,EAAC,QAAQ;cAACT,SAAS,EAAC,MAAM;cAAAC,QAAA,EACrCf;YAAK;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACR,eAEDjC,OAAA,CAACN,IAAI;cAACyC,QAAQ,EAAEhB,YAAa;cAAAO,QAAA,gBAC3B1B,OAAA,CAACN,IAAI,CAAC0C,KAAK;gBAACX,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1B,OAAA,CAACN,IAAI,CAAC2C,KAAK;kBAAAX,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjC,OAAA,CAACN,IAAI,CAAC4C,OAAO;kBACXC,IAAI,EAAC,MAAM;kBACXtB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEb,QAAQ,CAACE,QAAS;kBACzBiC,QAAQ,EAAE1B,YAAa;kBACvB2B,WAAW,EAAC,mBAAmB;kBAC/BC,QAAQ;kBACRC,QAAQ,EAAElC;gBAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjC,OAAA,CAACN,IAAI,CAAC0C,KAAK;gBAACX,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBAC1B1B,OAAA,CAACN,IAAI,CAAC2C,KAAK;kBAAAX,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACjCjC,OAAA,CAACN,IAAI,CAAC4C,OAAO;kBACXC,IAAI,EAAC,UAAU;kBACftB,IAAI,EAAC,UAAU;kBACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;kBACzBgC,QAAQ,EAAE1B,YAAa;kBACvB2B,WAAW,EAAC,mBAAmB;kBAC/BC,QAAQ;kBACRC,QAAQ,EAAElC;gBAAQ;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ,CAAC,eAEbjC,OAAA,CAACL,MAAM;gBACLuC,OAAO,EAAC,SAAS;gBACjBK,IAAI,EAAC,QAAQ;gBACbd,SAAS,EAAC,YAAY;gBACtBkB,QAAQ,EAAElC,OAAQ;gBAClBmC,IAAI,EAAC,IAAI;gBAAAlB,QAAA,EAERjB,OAAO,gBACNT,OAAA,CAAAE,SAAA;kBAAAwB,QAAA,gBACE1B,OAAA,CAACH,OAAO;oBACNgD,EAAE,EAAC,MAAM;oBACTC,SAAS,EAAC,QAAQ;oBAClBF,IAAI,EAAC,IAAI;oBACTG,IAAI,EAAC,QAAQ;oBACb,eAAY,MAAM;oBAClBtB,SAAS,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CAAC,iBAEJ;gBAAA,eAAE,CAAC,GAEH;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAEPjC,OAAA;cAAKyB,SAAS,EAAC,kBAAkB;cAAAC,QAAA,eAC/B1B,OAAA;gBAAOyB,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBAC3B1B,OAAA;kBAAA0B,QAAA,EAAQ;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAAAjC,OAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,2BACjB,eAAAjC,OAAA;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,qBAE/B;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEhB;AAAC7B,EAAA,CArHQD,KAAK;EAAA,QAQML,OAAO;AAAA;AAAAkD,EAAA,GARlB7C,KAAK;AAuHd,eAAeA,KAAK;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}