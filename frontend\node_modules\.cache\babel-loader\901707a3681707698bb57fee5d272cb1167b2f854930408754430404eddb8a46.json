{"ast": null, "code": "import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\nfunction useRafInterval(fn, ms, paused = false) {\n  let handle;\n  let start = new Date().getTime();\n  const fnRef = useCommittedRef(fn);\n  // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n  const pausedRef = useCommittedRef(paused);\n  function loop() {\n    const current = new Date().getTime();\n    const delta = current - start;\n    if (pausedRef.current) return;\n    if (delta >= ms && fnRef.current) {\n      fnRef.current();\n      start = new Date().getTime();\n    }\n    cancelAnimationFrame(handle);\n    handle = requestAnimationFrame(loop);\n  }\n  useEffect(() => {\n    handle = requestAnimationFrame(loop);\n    return () => cancelAnimationFrame(handle);\n  }, []);\n}\nexport default useRafInterval;", "map": {"version": 3, "names": ["useEffect", "useCommittedRef", "useRafInterval", "fn", "ms", "paused", "handle", "start", "Date", "getTime", "fnRef", "pausedRef", "loop", "current", "delta", "cancelAnimationFrame", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useRafInterval.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\nfunction useRafInterval(fn, ms, paused = false) {\n  let handle;\n  let start = new Date().getTime();\n  const fnRef = useCommittedRef(fn);\n  // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n  const pausedRef = useCommittedRef(paused);\n  function loop() {\n    const current = new Date().getTime();\n    const delta = current - start;\n    if (pausedRef.current) return;\n    if (delta >= ms && fnRef.current) {\n      fnRef.current();\n      start = new Date().getTime();\n    }\n    cancelAnimationFrame(handle);\n    handle = requestAnimationFrame(loop);\n  }\n  useEffect(() => {\n    handle = requestAnimationFrame(loop);\n    return () => cancelAnimationFrame(handle);\n  }, []);\n}\nexport default useRafInterval;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,cAAcA,CAACC,EAAE,EAAEC,EAAE,EAAEC,MAAM,GAAG,KAAK,EAAE;EAC9C,IAAIC,MAAM;EACV,IAAIC,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAChC,MAAMC,KAAK,GAAGT,eAAe,CAACE,EAAE,CAAC;EACjC;EACA;EACA,MAAMQ,SAAS,GAAGV,eAAe,CAACI,MAAM,CAAC;EACzC,SAASO,IAAIA,CAAA,EAAG;IACd,MAAMC,OAAO,GAAG,IAAIL,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACpC,MAAMK,KAAK,GAAGD,OAAO,GAAGN,KAAK;IAC7B,IAAII,SAAS,CAACE,OAAO,EAAE;IACvB,IAAIC,KAAK,IAAIV,EAAE,IAAIM,KAAK,CAACG,OAAO,EAAE;MAChCH,KAAK,CAACG,OAAO,CAAC,CAAC;MACfN,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC9B;IACAM,oBAAoB,CAACT,MAAM,CAAC;IAC5BA,MAAM,GAAGU,qBAAqB,CAACJ,IAAI,CAAC;EACtC;EACAZ,SAAS,CAAC,MAAM;IACdM,MAAM,GAAGU,qBAAqB,CAACJ,IAAI,CAAC;IACpC,OAAO,MAAMG,oBAAoB,CAACT,MAAM,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;AACR;AACA,eAAeJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}