{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'InputGroupContext';\nexport default context;", "map": {"version": 3, "names": ["React", "context", "createContext", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/InputGroupContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'InputGroupContext';\nexport default context;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACtDD,OAAO,CAACE,WAAW,GAAG,mBAAmB;AACzC,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}