from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user import User
from models.employee import Employee
from models.evaluation import Evaluation, db
from models.evaluation_result import EvaluationResult
from fuzzy_logic.engine import FuzzyLogicEngine
from datetime import datetime, date

evaluations_bp = Blueprint('evaluations', __name__)

@evaluations_bp.route('', methods=['GET'])
@jwt_required()
def get_evaluations():
    """Get all evaluations with optional filtering"""
    try:
        # Query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        employee_id = request.args.get('employee_id', type=int)
        department = request.args.get('department', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        # Build query
        query = Evaluation.query.join(Employee)
        
        if employee_id:
            query = query.filter(Evaluation.employee_id == employee_id)
        
        if department:
            query = query.filter(Employee.department == department)
        
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(Evaluation.evaluation_period_start >= start_date_obj)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format. Use YYYY-MM-DD'}), 400
        
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(Evaluation.evaluation_period_end <= end_date_obj)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format. Use YYYY-MM-DD'}), 400
        
        # Paginate results
        evaluations = query.order_by(Evaluation.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'evaluations': [eval.to_dict() for eval in evaluations.items],
            'pagination': {
                'page': evaluations.page,
                'pages': evaluations.pages,
                'per_page': evaluations.per_page,
                'total': evaluations.total,
                'has_next': evaluations.has_next,
                'has_prev': evaluations.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@evaluations_bp.route('/<int:evaluation_id>', methods=['GET'])
@jwt_required()
def get_evaluation(evaluation_id):
    """Get specific evaluation by ID"""
    try:
        evaluation = Evaluation.query.get(evaluation_id)

        if not evaluation:
            return jsonify({'error': 'Evaluation not found'}), 404

        return jsonify(evaluation.to_dict()), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@evaluations_bp.route('', methods=['POST'])
@jwt_required()
def create_evaluation():
    """Create new evaluation and process with fuzzy logic"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        # Check if user has permission (admin or hrd)
        if user.role not in ['admin', 'hrd']:
            return jsonify({'error': 'Insufficient permissions'}), 403

        data = request.get_json()

        # Validate required fields
        required_fields = [
            'employee_id', 'evaluation_period_start', 'evaluation_period_end',
            'attendance_score', 'punctuality_score', 'compliance_score',
            'total_work_days', 'present_days', 'late_arrivals', 'violations_count'
        ]

        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'{field} is required'}), 400

        # Validate employee exists
        employee = Employee.query.get(data['employee_id'])
        if not employee or not employee.is_active:
            return jsonify({'error': 'Employee not found or inactive'}), 404

        # Parse dates
        try:
            period_start = datetime.strptime(data['evaluation_period_start'], '%Y-%m-%d').date()
            period_end = datetime.strptime(data['evaluation_period_end'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400

        # Validate date range
        if period_start >= period_end:
            return jsonify({'error': 'Start date must be before end date'}), 400

        # Validate scores (0-100)
        score_fields = ['attendance_score', 'punctuality_score', 'compliance_score']
        for field in score_fields:
            score = float(data[field])
            if score < 0 or score > 100:
                return jsonify({'error': f'{field} must be between 0 and 100'}), 400

        # Create evaluation
        evaluation = Evaluation(
            employee_id=data['employee_id'],
            evaluator_id=current_user_id,
            evaluation_period_start=period_start,
            evaluation_period_end=period_end,
            attendance_score=data['attendance_score'],
            punctuality_score=data['punctuality_score'],
            compliance_score=data['compliance_score'],
            total_work_days=data['total_work_days'],
            present_days=data['present_days'],
            late_arrivals=data['late_arrivals'],
            violations_count=data['violations_count'],
            notes=data.get('notes', '')
        )

        db.session.add(evaluation)
        db.session.flush()  # Get the evaluation ID

        # Process with fuzzy logic
        fuzzy_engine = FuzzyLogicEngine()
        fuzzy_result = fuzzy_engine.evaluate(
            attendance=float(data['attendance_score']),
            punctuality=float(data['punctuality_score']),
            compliance=float(data['compliance_score'])
        )

        # Create evaluation result
        result = EvaluationResult(
            evaluation_id=evaluation.id,
            attendance_fuzzy_low=fuzzy_result['fuzzy_values']['attendance']['low'],
            attendance_fuzzy_medium=fuzzy_result['fuzzy_values']['attendance']['medium'],
            attendance_fuzzy_high=fuzzy_result['fuzzy_values']['attendance']['high'],
            punctuality_fuzzy_low=fuzzy_result['fuzzy_values']['punctuality']['low'],
            punctuality_fuzzy_medium=fuzzy_result['fuzzy_values']['punctuality']['medium'],
            punctuality_fuzzy_high=fuzzy_result['fuzzy_values']['punctuality']['high'],
            compliance_fuzzy_low=fuzzy_result['fuzzy_values']['compliance']['low'],
            compliance_fuzzy_medium=fuzzy_result['fuzzy_values']['compliance']['medium'],
            compliance_fuzzy_high=fuzzy_result['fuzzy_values']['compliance']['high'],
            discipline_score=fuzzy_result['discipline_score'],
            discipline_level=fuzzy_result['discipline_level'],
            confidence_level=fuzzy_result['confidence_level']
        )

        db.session.add(result)
        db.session.commit()

        return jsonify({
            'message': 'Evaluation created and processed successfully',
            'evaluation': evaluation.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@evaluations_bp.route('/<int:evaluation_id>', methods=['PUT'])
@jwt_required()
def update_evaluation(evaluation_id):
    """Update evaluation and reprocess with fuzzy logic"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        # Check if user has permission (admin or hrd)
        if user.role not in ['admin', 'hrd']:
            return jsonify({'error': 'Insufficient permissions'}), 403

        evaluation = Evaluation.query.get(evaluation_id)
        if not evaluation:
            return jsonify({'error': 'Evaluation not found'}), 404

        data = request.get_json()

        # Update fields if provided
        if 'evaluation_period_start' in data:
            try:
                evaluation.evaluation_period_start = datetime.strptime(data['evaluation_period_start'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'Invalid evaluation_period_start format. Use YYYY-MM-DD'}), 400

        if 'evaluation_period_end' in data:
            try:
                evaluation.evaluation_period_end = datetime.strptime(data['evaluation_period_end'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'Invalid evaluation_period_end format. Use YYYY-MM-DD'}), 400

        # Validate date range if both dates are provided
        if evaluation.evaluation_period_start >= evaluation.evaluation_period_end:
            return jsonify({'error': 'Start date must be before end date'}), 400

        # Update scores and validate
        score_fields = ['attendance_score', 'punctuality_score', 'compliance_score']
        scores_updated = False

        for field in score_fields:
            if field in data:
                score = float(data[field])
                if score < 0 or score > 100:
                    return jsonify({'error': f'{field} must be between 0 and 100'}), 400
                setattr(evaluation, field, score)
                scores_updated = True

        # Update other fields
        numeric_fields = ['total_work_days', 'present_days', 'late_arrivals', 'violations_count']
        for field in numeric_fields:
            if field in data:
                setattr(evaluation, field, data[field])

        if 'notes' in data:
            evaluation.notes = data['notes']

        # If scores were updated, reprocess with fuzzy logic
        if scores_updated:
            fuzzy_engine = FuzzyLogicEngine()
            fuzzy_result = fuzzy_engine.evaluate(
                attendance=float(evaluation.attendance_score),
                punctuality=float(evaluation.punctuality_score),
                compliance=float(evaluation.compliance_score)
            )

            # Update or create evaluation result
            if evaluation.result:
                result = evaluation.result
            else:
                result = EvaluationResult(evaluation_id=evaluation.id)
                db.session.add(result)

            # Update fuzzy values
            result.attendance_fuzzy_low = fuzzy_result['fuzzy_values']['attendance']['low']
            result.attendance_fuzzy_medium = fuzzy_result['fuzzy_values']['attendance']['medium']
            result.attendance_fuzzy_high = fuzzy_result['fuzzy_values']['attendance']['high']
            result.punctuality_fuzzy_low = fuzzy_result['fuzzy_values']['punctuality']['low']
            result.punctuality_fuzzy_medium = fuzzy_result['fuzzy_values']['punctuality']['medium']
            result.punctuality_fuzzy_high = fuzzy_result['fuzzy_values']['punctuality']['high']
            result.compliance_fuzzy_low = fuzzy_result['fuzzy_values']['compliance']['low']
            result.compliance_fuzzy_medium = fuzzy_result['fuzzy_values']['compliance']['medium']
            result.compliance_fuzzy_high = fuzzy_result['fuzzy_values']['compliance']['high']
            result.discipline_score = fuzzy_result['discipline_score']
            result.discipline_level = fuzzy_result['discipline_level']
            result.confidence_level = fuzzy_result['confidence_level']

        db.session.commit()

        return jsonify({
            'message': 'Evaluation updated successfully',
            'evaluation': evaluation.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@evaluations_bp.route('/<int:evaluation_id>', methods=['DELETE'])
@jwt_required()
def delete_evaluation(evaluation_id):
    """Delete evaluation"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)

        # Check if user has permission (admin only)
        if user.role != 'admin':
            return jsonify({'error': 'Insufficient permissions'}), 403

        evaluation = Evaluation.query.get(evaluation_id)
        if not evaluation:
            return jsonify({'error': 'Evaluation not found'}), 404

        db.session.delete(evaluation)
        db.session.commit()

        return jsonify({'message': 'Evaluation deleted successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@evaluations_bp.route('/employee/<int:employee_id>', methods=['GET'])
@jwt_required()
def get_employee_evaluations(employee_id):
    """Get all evaluations for a specific employee"""
    try:
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404

        evaluations = Evaluation.query.filter_by(employee_id=employee_id).order_by(
            Evaluation.evaluation_period_end.desc()
        ).all()

        return jsonify({
            'employee': employee.to_dict(),
            'evaluations': [eval.to_dict() for eval in evaluations]
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500
