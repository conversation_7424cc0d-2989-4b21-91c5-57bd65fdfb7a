{"ast": null, "code": "import Tabs from '@restart/ui/Tabs';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabContainer = ({\n  transition,\n  ...props\n}) => /*#__PURE__*/_jsx(Tabs, {\n  ...props,\n  transition: getTabTransitionComponent(transition)\n});\nTabContainer.displayName = 'TabContainer';\nexport default TabContainer;", "map": {"version": 3, "names": ["Tabs", "getTabTransitionComponent", "jsx", "_jsx", "TabContainer", "transition", "props", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/TabContainer.js"], "sourcesContent": ["import Tabs from '@restart/ui/Tabs';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabContainer = ({\n  transition,\n  ...props\n}) => /*#__PURE__*/_jsx(Tabs, {\n  ...props,\n  transition: getTabTransitionComponent(transition)\n});\nTabContainer.displayName = 'TabContainer';\nexport default TabContainer;"], "mappings": "AAAA,OAAOA,IAAI,MAAM,kBAAkB;AACnC,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAGA,CAAC;EACpBC,UAAU;EACV,GAAGC;AACL,CAAC,KAAK,aAAaH,IAAI,CAACH,IAAI,EAAE;EAC5B,GAAGM,KAAK;EACRD,UAAU,EAAEJ,yBAAyB,CAACI,UAAU;AAClD,CAAC,CAAC;AACFD,YAAY,CAACG,WAAW,GAAG,cAAc;AACzC,eAAeH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}