#!/usr/bin/env python3
"""
Setup script for Fuzzy Logic Employee Discipline Evaluation System
"""

import os
import sys
import subprocess
import platform

def run_command(command, cwd=None):
    """Run a command and return success status"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error running command: {command}")
            print(f"Error output: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"Exception running command {command}: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        print(f"Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python version {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def check_node_version():
    """Check if Node.js version is compatible"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Node.js is not installed")
            return False
        
        version = result.stdout.strip().replace('v', '')
        major_version = int(version.split('.')[0])
        
        if major_version < 16:
            print(f"❌ Node.js 16 or higher is required. Current version: {version}")
            return False
        
        print(f"✅ Node.js version {version} is compatible")
        return True
    except Exception as e:
        print(f"❌ Error checking Node.js version: {e}")
        return False

def setup_backend():
    """Set up the backend environment"""
    print("\n🔧 Setting up backend...")
    
    backend_dir = os.path.join(os.getcwd(), 'backend')
    
    # Create virtual environment
    print("Creating virtual environment...")
    venv_command = "python -m venv venv"
    if not run_command(venv_command, cwd=backend_dir):
        return False
    
    # Determine activation script based on OS
    if platform.system() == "Windows":
        activate_script = os.path.join(backend_dir, 'venv', 'Scripts', 'activate')
        pip_command = os.path.join(backend_dir, 'venv', 'Scripts', 'pip')
    else:
        activate_script = os.path.join(backend_dir, 'venv', 'bin', 'activate')
        pip_command = os.path.join(backend_dir, 'venv', 'bin', 'pip')
    
    # Install requirements
    print("Installing Python dependencies...")
    install_command = f'"{pip_command}" install -r requirements.txt'
    if not run_command(install_command, cwd=backend_dir):
        return False
    
    # Copy environment file
    env_example = os.path.join(backend_dir, '.env.example')
    env_file = os.path.join(backend_dir, '.env')
    
    if os.path.exists(env_example) and not os.path.exists(env_file):
        print("Creating .env file from template...")
        try:
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ .env file created. Please edit it with your database credentials.")
        except Exception as e:
            print(f"❌ Error creating .env file: {e}")
            return False
    
    print("✅ Backend setup completed!")
    return True

def setup_frontend():
    """Set up the frontend environment"""
    print("\n🔧 Setting up frontend...")
    
    frontend_dir = os.path.join(os.getcwd(), 'frontend')
    
    # Install npm dependencies
    print("Installing Node.js dependencies...")
    install_command = "npm install"
    if not run_command(install_command, cwd=frontend_dir):
        print("Trying with yarn...")
        install_command = "yarn install"
        if not run_command(install_command, cwd=frontend_dir):
            return False
    
    print("✅ Frontend setup completed!")
    return True

def test_fuzzy_logic():
    """Test the fuzzy logic engine"""
    print("\n🧪 Testing fuzzy logic engine...")
    
    backend_dir = os.path.join(os.getcwd(), 'backend')
    
    # Determine python executable in virtual environment
    if platform.system() == "Windows":
        python_exe = os.path.join(backend_dir, 'venv', 'Scripts', 'python')
    else:
        python_exe = os.path.join(backend_dir, 'venv', 'bin', 'python')
    
    test_command = f'"{python_exe}" fuzzy_logic/test_engine.py'
    
    try:
        result = subprocess.run(test_command, shell=True, cwd=backend_dir, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Fuzzy logic engine test passed!")
            print("Sample output:")
            print(result.stdout[-500:])  # Show last 500 characters
            return True
        else:
            print("❌ Fuzzy logic engine test failed!")
            print("Error output:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ Error running fuzzy logic test: {e}")
        return False

def create_start_scripts():
    """Create convenient start scripts"""
    print("\n📝 Creating start scripts...")
    
    # Backend start script
    if platform.system() == "Windows":
        backend_script = """@echo off
cd backend
call venv\\Scripts\\activate
python app.py
"""
        with open('start_backend.bat', 'w') as f:
            f.write(backend_script)
        
        frontend_script = """@echo off
cd frontend
npm start
"""
        with open('start_frontend.bat', 'w') as f:
            f.write(frontend_script)
        
        print("✅ Created start_backend.bat and start_frontend.bat")
    else:
        backend_script = """#!/bin/bash
cd backend
source venv/bin/activate
python app.py
"""
        with open('start_backend.sh', 'w') as f:
            f.write(backend_script)
        os.chmod('start_backend.sh', 0o755)
        
        frontend_script = """#!/bin/bash
cd frontend
npm start
"""
        with open('start_frontend.sh', 'w') as f:
            f.write(frontend_script)
        os.chmod('start_frontend.sh', 0o755)
        
        print("✅ Created start_backend.sh and start_frontend.sh")

def main():
    """Main setup function"""
    print("🚀 Fuzzy Logic Employee Discipline Evaluation System Setup")
    print("=" * 60)
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_node_version():
        print("Please install Node.js 16+ from https://nodejs.org/")
        return False
    
    # Setup backend
    if not setup_backend():
        print("❌ Backend setup failed!")
        return False
    
    # Setup frontend
    if not setup_frontend():
        print("❌ Frontend setup failed!")
        return False
    
    # Test fuzzy logic
    if not test_fuzzy_logic():
        print("⚠️  Fuzzy logic test failed, but setup continues...")
    
    # Create start scripts
    create_start_scripts()
    
    print("\n" + "=" * 60)
    print("🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit backend/.env with your database credentials")
    print("2. Set up your database (MySQL or PostgreSQL)")
    print("3. Initialize the database: cd backend && python init_db.py")
    print("4. Start the backend server")
    print("5. Start the frontend server")
    print("\nDefault login credentials:")
    print("  Admin: admin / admin123")
    print("  HRD: hrd / hrd123")
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
