import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ge, Spinner, Alert } from 'react-bootstrap';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

function Dashboard() {
  const [stats, setStats] = useState({
    totalEmployees: 0,
    totalEvaluations: 0,
    recentEvaluations: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  const { user } = useAuth();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch employees count
      const employeesResponse = await axios.get('/api/employees?per_page=1');
      
      // Fetch recent evaluations
      const evaluationsResponse = await axios.get('/api/evaluations?per_page=5');
      
      setStats({
        totalEmployees: employeesResponse.data.pagination.total,
        totalEvaluations: evaluationsResponse.data.pagination.total,
        recentEvaluations: evaluationsResponse.data.evaluations
      });
      
    } catch (err) {
      setError('Gagal memuat data dashboard');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getDisciplineBadgeVariant = (level) => {
    switch (level) {
      case 'Sangat Disiplin':
        return 'success';
      case 'Disiplin':
        return 'info';
      case 'Cukup':
        return 'warning';
      case 'Kurang':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">Dashboard</h1>
          <p className="text-muted">Selamat datang, {user?.full_name}</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={6} lg={3} className="mb-3">
          <Card className="h-100">
            <Card.Body className="text-center">
              <div className="display-6 text-primary mb-2">
                <i className="bi bi-people-fill"></i>
              </div>
              <h5 className="card-title">Total Karyawan</h5>
              <h2 className="text-primary">{stats.totalEmployees}</h2>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6} lg={3} className="mb-3">
          <Card className="h-100">
            <Card.Body className="text-center">
              <div className="display-6 text-info mb-2">
                <i className="bi bi-clipboard-check-fill"></i>
              </div>
              <h5 className="card-title">Total Evaluasi</h5>
              <h2 className="text-info">{stats.totalEvaluations}</h2>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6} lg={3} className="mb-3">
          <Card className="h-100">
            <Card.Body className="text-center">
              <div className="display-6 text-success mb-2">
                <i className="bi bi-graph-up-arrow"></i>
              </div>
              <h5 className="card-title">Evaluasi Bulan Ini</h5>
              <h2 className="text-success">
                {stats.recentEvaluations.filter(eval => {
                  const evalDate = new Date(eval.created_at);
                  const now = new Date();
                  return evalDate.getMonth() === now.getMonth() && 
                         evalDate.getFullYear() === now.getFullYear();
                }).length}
              </h2>
            </Card.Body>
          </Card>
        </Col>
        
        <Col md={6} lg={3} className="mb-3">
          <Card className="h-100">
            <Card.Body className="text-center">
              <div className="display-6 text-warning mb-2">
                <i className="bi bi-award-fill"></i>
              </div>
              <h5 className="card-title">Sangat Disiplin</h5>
              <h2 className="text-warning">
                {stats.recentEvaluations.filter(eval => 
                  eval.result?.discipline_level === 'Sangat Disiplin'
                ).length}
              </h2>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Quick Actions */}
      <Row className="mb-4">
        <Col>
          <Card>
            <Card.Header>
              <h5 className="mb-0">Aksi Cepat</h5>
            </Card.Header>
            <Card.Body>
              <Row>
                <Col md={6} lg={3} className="mb-2">
                  <Button as={Link} to="/employees" variant="outline-primary" className="w-100">
                    <i className="bi bi-people me-2"></i>
                    Kelola Karyawan
                  </Button>
                </Col>
                <Col md={6} lg={3} className="mb-2">
                  <Button as={Link} to="/evaluation" variant="outline-success" className="w-100">
                    <i className="bi bi-clipboard-plus me-2"></i>
                    Evaluasi Baru
                  </Button>
                </Col>
                <Col md={6} lg={3} className="mb-2">
                  <Button as={Link} to="/results" variant="outline-info" className="w-100">
                    <i className="bi bi-bar-chart me-2"></i>
                    Lihat Hasil
                  </Button>
                </Col>
                <Col md={6} lg={3} className="mb-2">
                  <Button as={Link} to="/reports" variant="outline-warning" className="w-100">
                    <i className="bi bi-file-earmark-pdf me-2"></i>
                    Laporan
                  </Button>
                </Col>
              </Row>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Recent Evaluations */}
      <Row>
        <Col>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h5 className="mb-0">Evaluasi Terbaru</h5>
              <Button as={Link} to="/results" variant="outline-primary" size="sm">
                Lihat Semua
              </Button>
            </Card.Header>
            <Card.Body>
              {stats.recentEvaluations.length === 0 ? (
                <div className="text-center py-4">
                  <p className="text-muted">Belum ada evaluasi</p>
                  <Button as={Link} to="/evaluation" variant="primary">
                    Buat Evaluasi Pertama
                  </Button>
                </div>
              ) : (
                <Table responsive hover>
                  <thead>
                    <tr>
                      <th>Karyawan</th>
                      <th>Periode</th>
                      <th>Skor</th>
                      <th>Tingkat</th>
                      <th>Tanggal</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stats.recentEvaluations.map((evaluation) => (
                      <tr key={evaluation.id}>
                        <td>
                          <strong>{evaluation.employee_name}</strong>
                          <br />
                          <small className="text-muted">{evaluation.employee_employee_id}</small>
                        </td>
                        <td>
                          <small>
                            {new Date(evaluation.evaluation_period_start).toLocaleDateString('id-ID')} - 
                            {new Date(evaluation.evaluation_period_end).toLocaleDateString('id-ID')}
                          </small>
                        </td>
                        <td>
                          <strong>{evaluation.result?.discipline_score || 'N/A'}</strong>
                        </td>
                        <td>
                          <Badge bg={getDisciplineBadgeVariant(evaluation.result?.discipline_level)}>
                            {evaluation.result?.discipline_level || 'Belum Diproses'}
                          </Badge>
                        </td>
                        <td>
                          <small>{new Date(evaluation.created_at).toLocaleDateString('id-ID')}</small>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
}

export default Dashboard;
