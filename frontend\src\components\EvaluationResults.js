import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Table, Badge, Spin<PERSON>, 
  <PERSON><PERSON>, <PERSON>dal, ProgressBar, InputGroup, Form 
} from 'react-bootstrap';
import { Link, useLocation } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';

function EvaluationResults() {
  const location = useLocation();
  const [evaluations, setEvaluations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedEvaluation, setSelectedEvaluation] = useState(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [pagination, setPagination] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    department: '',
    discipline_level: ''
  });

  useEffect(() => {
    fetchEvaluations();
    
    // If coming from new evaluation, highlight it
    if (location.state?.newEvaluationId) {
      setTimeout(() => {
        const element = document.getElementById(`evaluation-${location.state.newEvaluationId}`);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
          element.classList.add('table-warning');
          setTimeout(() => element.classList.remove('table-warning'), 3000);
        }
      }, 500);
    }
  }, [currentPage, filters, location.state]);

  const fetchEvaluations = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: 10,
        ...filters
      };
      
      const response = await axios.get('/api/evaluations', { params });
      setEvaluations(response.data.evaluations);
      setPagination(response.data.pagination);
    } catch (err) {
      setError('Gagal memuat data evaluasi');
      console.error('Fetch evaluations error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleShowDetail = async (evaluation) => {
    try {
      const response = await axios.get(`/api/evaluations/${evaluation.id}`);
      setSelectedEvaluation(response.data);
      setShowDetailModal(true);
    } catch (err) {
      toast.error('Gagal memuat detail evaluasi');
      console.error('Fetch evaluation detail error:', err);
    }
  };

  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setSelectedEvaluation(null);
  };

  const handleGenerateReport = async (evaluationId) => {
    try {
      const response = await axios.post('/api/reports/generate', {
        evaluation_id: evaluationId,
        report_type: 'individual'
      });
      
      toast.success('Laporan berhasil dibuat!');
      
      // Download the report
      const downloadResponse = await axios.get(`/api/reports/${response.data.report.id}/download`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([downloadResponse.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', response.data.report.file_name);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Gagal membuat laporan';
      toast.error(errorMessage);
      console.error('Generate report error:', err);
    }
  };

  const getDisciplineBadgeVariant = (level) => {
    switch (level) {
      case 'Sangat Disiplin':
        return 'success';
      case 'Disiplin':
        return 'info';
      case 'Cukup':
        return 'warning';
      case 'Kurang':
        return 'danger';
      default:
        return 'secondary';
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'info';
    if (score >= 40) return 'warning';
    return 'danger';
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setCurrentPage(1);
  };

  if (loading && evaluations.length === 0) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">Hasil Evaluasi Kedisiplinan</h1>
          <p className="text-muted">Hasil evaluasi menggunakan fuzzy logic</p>
        </Col>
        <Col xs="auto">
          <Button as={Link} to="/evaluation" variant="primary">
            <i className="bi bi-plus-circle me-2"></i>
            Evaluasi Baru
          </Button>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      <Card>
        <Card.Header>
          <Row className="align-items-center">
            <Col>
              <h5 className="mb-0">Daftar Evaluasi</h5>
            </Col>
          </Row>
        </Card.Header>
        
        <Card.Body>
          {/* Filters */}
          <Row className="mb-3">
            <Col md={4}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Cari karyawan..."
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                />
              </InputGroup>
            </Col>
            <Col md={3}>
              <Form.Select name="department" value={filters.department} onChange={handleFilterChange}>
                <option value="">Semua Departemen</option>
                <option value="IT">IT</option>
                <option value="Finance">Finance</option>
                <option value="Marketing">Marketing</option>
                <option value="HR">HR</option>
                <option value="Operations">Operations</option>
              </Form.Select>
            </Col>
            <Col md={3}>
              <Form.Select name="discipline_level" value={filters.discipline_level} onChange={handleFilterChange}>
                <option value="">Semua Tingkat</option>
                <option value="Sangat Disiplin">Sangat Disiplin</option>
                <option value="Disiplin">Disiplin</option>
                <option value="Cukup">Cukup</option>
                <option value="Kurang">Kurang</option>
              </Form.Select>
            </Col>
          </Row>

          {/* Results Table */}
          <Table responsive hover>
            <thead>
              <tr>
                <th>Karyawan</th>
                <th>Periode</th>
                <th>Skor Input</th>
                <th>Skor Kedisiplinan</th>
                <th>Tingkat</th>
                <th>Confidence</th>
                <th>Tanggal</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              {evaluations.map((evaluation) => (
                <tr key={evaluation.id} id={`evaluation-${evaluation.id}`}>
                  <td>
                    <div>
                      <strong>{evaluation.employee_name}</strong>
                      <br />
                      <small className="text-muted">{evaluation.employee_employee_id}</small>
                    </div>
                  </td>
                  <td>
                    <small>
                      {new Date(evaluation.evaluation_period_start).toLocaleDateString('id-ID')} - 
                      {new Date(evaluation.evaluation_period_end).toLocaleDateString('id-ID')}
                    </small>
                  </td>
                  <td>
                    <small>
                      A: {evaluation.attendance_score}%<br />
                      P: {evaluation.punctuality_score}%<br />
                      C: {evaluation.compliance_score}%
                    </small>
                  </td>
                  <td>
                    <strong className={`text-${getScoreColor(evaluation.result?.discipline_score || 0)}`}>
                      {evaluation.result?.discipline_score || 'N/A'}
                    </strong>
                  </td>
                  <td>
                    <Badge bg={getDisciplineBadgeVariant(evaluation.result?.discipline_level)}>
                      {evaluation.result?.discipline_level || 'Belum Diproses'}
                    </Badge>
                  </td>
                  <td>
                    <small>{evaluation.result?.confidence_level || 'N/A'}%</small>
                  </td>
                  <td>
                    <small>{new Date(evaluation.created_at).toLocaleDateString('id-ID')}</small>
                  </td>
                  <td>
                    <div className="d-flex gap-1">
                      <Button
                        variant="outline-info"
                        size="sm"
                        onClick={() => handleShowDetail(evaluation)}
                        title="Lihat Detail"
                      >
                        <i className="bi bi-eye"></i>
                      </Button>
                      <Button
                        variant="outline-success"
                        size="sm"
                        onClick={() => handleGenerateReport(evaluation.id)}
                        title="Generate Report"
                      >
                        <i className="bi bi-file-earmark-pdf"></i>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>

          {evaluations.length === 0 && !loading && (
            <div className="text-center py-4">
              <p className="text-muted">Tidak ada data evaluasi</p>
              <Button as={Link} to="/evaluation" variant="primary">
                Buat Evaluasi Pertama
              </Button>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Detail Modal */}
      <Modal show={showDetailModal} onHide={handleCloseDetail} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>Detail Evaluasi Kedisiplinan</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {selectedEvaluation && (
            <div>
              {/* Employee Info */}
              <Card className="mb-3">
                <Card.Header>
                  <h6 className="mb-0">Informasi Karyawan</h6>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={6}>
                      <p><strong>Nama:</strong> {selectedEvaluation.employee_name}</p>
                      <p><strong>ID:</strong> {selectedEvaluation.employee_employee_id}</p>
                    </Col>
                    <Col md={6}>
                      <p><strong>Periode:</strong> {new Date(selectedEvaluation.evaluation_period_start).toLocaleDateString('id-ID')} - {new Date(selectedEvaluation.evaluation_period_end).toLocaleDateString('id-ID')}</p>
                      <p><strong>Evaluator:</strong> {selectedEvaluation.evaluator_name}</p>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>

              {/* Input Scores */}
              <Card className="mb-3">
                <Card.Header>
                  <h6 className="mb-0">Skor Input</h6>
                </Card.Header>
                <Card.Body>
                  <Row>
                    <Col md={4}>
                      <div className="text-center">
                        <h5>Kehadiran</h5>
                        <ProgressBar 
                          variant={getScoreColor(selectedEvaluation.attendance_score)}
                          now={selectedEvaluation.attendance_score}
                          label={`${selectedEvaluation.attendance_score}%`}
                        />
                        <small className="text-muted">
                          {selectedEvaluation.present_days}/{selectedEvaluation.total_work_days} hari
                        </small>
                      </div>
                    </Col>
                    <Col md={4}>
                      <div className="text-center">
                        <h5>Ketepatan Waktu</h5>
                        <ProgressBar 
                          variant={getScoreColor(selectedEvaluation.punctuality_score)}
                          now={selectedEvaluation.punctuality_score}
                          label={`${selectedEvaluation.punctuality_score}%`}
                        />
                        <small className="text-muted">
                          {selectedEvaluation.late_arrivals} kali terlambat
                        </small>
                      </div>
                    </Col>
                    <Col md={4}>
                      <div className="text-center">
                        <h5>Kepatuhan</h5>
                        <ProgressBar 
                          variant={getScoreColor(selectedEvaluation.compliance_score)}
                          now={selectedEvaluation.compliance_score}
                          label={`${selectedEvaluation.compliance_score}%`}
                        />
                        <small className="text-muted">
                          {selectedEvaluation.violations_count} pelanggaran
                        </small>
                      </div>
                    </Col>
                  </Row>
                </Card.Body>
              </Card>

              {/* Fuzzy Logic Results */}
              {selectedEvaluation.result && (
                <Card className="mb-3">
                  <Card.Header>
                    <h6 className="mb-0">Hasil Fuzzy Logic</h6>
                  </Card.Header>
                  <Card.Body>
                    <Row className="text-center mb-3">
                      <Col md={4}>
                        <h3 className={`text-${getScoreColor(selectedEvaluation.result.discipline_score)}`}>
                          {selectedEvaluation.result.discipline_score}
                        </h3>
                        <p>Skor Kedisiplinan</p>
                      </Col>
                      <Col md={4}>
                        <h3>
                          <Badge bg={getDisciplineBadgeVariant(selectedEvaluation.result.discipline_level)} className="fs-6">
                            {selectedEvaluation.result.discipline_level}
                          </Badge>
                        </h3>
                        <p>Tingkat Kedisiplinan</p>
                      </Col>
                      <Col md={4}>
                        <h3 className="text-info">{selectedEvaluation.result.confidence_level}%</h3>
                        <p>Confidence Level</p>
                      </Col>
                    </Row>

                    {/* Fuzzy Membership Values */}
                    <h6>Nilai Keanggotaan Fuzzy:</h6>
                    <Row>
                      <Col md={4}>
                        <h6>Kehadiran</h6>
                        <small>
                          Low: {selectedEvaluation.result.fuzzy_values?.attendance?.low?.toFixed(3) || 'N/A'}<br />
                          Medium: {selectedEvaluation.result.fuzzy_values?.attendance?.medium?.toFixed(3) || 'N/A'}<br />
                          High: {selectedEvaluation.result.fuzzy_values?.attendance?.high?.toFixed(3) || 'N/A'}
                        </small>
                      </Col>
                      <Col md={4}>
                        <h6>Ketepatan Waktu</h6>
                        <small>
                          Low: {selectedEvaluation.result.fuzzy_values?.punctuality?.low?.toFixed(3) || 'N/A'}<br />
                          Medium: {selectedEvaluation.result.fuzzy_values?.punctuality?.medium?.toFixed(3) || 'N/A'}<br />
                          High: {selectedEvaluation.result.fuzzy_values?.punctuality?.high?.toFixed(3) || 'N/A'}
                        </small>
                      </Col>
                      <Col md={4}>
                        <h6>Kepatuhan</h6>
                        <small>
                          Low: {selectedEvaluation.result.fuzzy_values?.compliance?.low?.toFixed(3) || 'N/A'}<br />
                          Medium: {selectedEvaluation.result.fuzzy_values?.compliance?.medium?.toFixed(3) || 'N/A'}<br />
                          High: {selectedEvaluation.result.fuzzy_values?.compliance?.high?.toFixed(3) || 'N/A'}
                        </small>
                      </Col>
                    </Row>
                  </Card.Body>
                </Card>
              )}

              {/* Notes */}
              {selectedEvaluation.notes && (
                <Card>
                  <Card.Header>
                    <h6 className="mb-0">Catatan</h6>
                  </Card.Header>
                  <Card.Body>
                    <p>{selectedEvaluation.notes}</p>
                  </Card.Body>
                </Card>
              )}
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={handleCloseDetail}>
            Tutup
          </Button>
          {selectedEvaluation && (
            <Button 
              variant="success" 
              onClick={() => handleGenerateReport(selectedEvaluation.id)}
            >
              <i className="bi bi-file-earmark-pdf me-2"></i>
              Generate Report
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </Container>
  );
}

export default EvaluationResults;
