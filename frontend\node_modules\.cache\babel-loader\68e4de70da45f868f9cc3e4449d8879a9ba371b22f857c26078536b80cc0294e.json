{"ast": null, "code": "import { useReducer } from 'react';\n\n/**\n * Returns a function that triggers a component update. the hook equivalent to\n * `this.forceUpdate()` in a class component. In most cases using a state value directly\n * is preferable but may be required in some advanced usages of refs for interop or\n * when direct DOM manipulation is required.\n *\n * ```ts\n * const forceUpdate = useForceUpdate();\n *\n * const updateOnClick = useCallback(() => {\n *  forceUpdate()\n * }, [forceUpdate])\n *\n * return <button type=\"button\" onClick={updateOnClick}>Hi there</button>\n * ```\n */\nexport default function useForceUpdate() {\n  // The toggling state value is designed to defeat React optimizations for skipping\n  // updates when they are strictly equal to the last state value\n  const [, dispatch] = useReducer(revision => revision + 1, 0);\n  return dispatch;\n}", "map": {"version": 3, "names": ["useReducer", "useForceUpdate", "dispatch", "revision"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useForceUpdate.js"], "sourcesContent": ["import { useReducer } from 'react';\n\n/**\n * Returns a function that triggers a component update. the hook equivalent to\n * `this.forceUpdate()` in a class component. In most cases using a state value directly\n * is preferable but may be required in some advanced usages of refs for interop or\n * when direct DOM manipulation is required.\n *\n * ```ts\n * const forceUpdate = useForceUpdate();\n *\n * const updateOnClick = useCallback(() => {\n *  forceUpdate()\n * }, [forceUpdate])\n *\n * return <button type=\"button\" onClick={updateOnClick}>Hi there</button>\n * ```\n */\nexport default function useForceUpdate() {\n  // The toggling state value is designed to defeat React optimizations for skipping\n  // updates when they are strictly equal to the last state value\n  const [, dispatch] = useReducer(revision => revision + 1, 0);\n  return dispatch;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAAA,EAAG;EACvC;EACA;EACA,MAAM,GAAGC,QAAQ,CAAC,GAAGF,UAAU,CAACG,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5D,OAAOD,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}