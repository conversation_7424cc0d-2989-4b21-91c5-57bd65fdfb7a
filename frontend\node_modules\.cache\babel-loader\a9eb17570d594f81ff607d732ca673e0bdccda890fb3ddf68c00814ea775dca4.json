{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\FUZYY LOGIC\\\\frontend\\\\src\\\\index.js\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: [/*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {\n    position: \"top-right\",\n    autoClose: 5000,\n    hideProgressBar: false,\n    newestOnTop: false,\n    closeOnClick: true,\n    rtl: false,\n    pauseOnFocusLoss: true,\n    draggable: true,\n    pauseOnHover: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 11,\n  columnNumber: 3\n}, this));", "map": {"version": 3, "names": ["React", "ReactDOM", "App", "ToastContainer", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "autoClose", "hideProgressBar", "newestOnTop", "closeOnClick", "rtl", "pauseOnFocusLoss", "draggable", "pauseOnHover"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/src/index.js"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport './index.css';\nimport App from './App';\nimport 'bootstrap/dist/css/bootstrap.min.css';\nimport { ToastContainer } from 'react-toastify';\nimport 'react-toastify/dist/ReactToastify.css';\n\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(\n  <React.StrictMode>\n    <App />\n    <ToastContainer\n      position=\"top-right\"\n      autoClose={5000}\n      hideProgressBar={false}\n      newestOnTop={false}\n      closeOnClick\n      rtl={false}\n      pauseOnFocusLoss\n      draggable\n      pauseOnHover\n    />\n  </React.StrictMode>\n);\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAO,sCAAsC;AAC7C,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,IAAI,GAAGL,QAAQ,CAACM,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cACTL,OAAA,CAACL,KAAK,CAACW,UAAU;EAAAC,QAAA,gBACfP,OAAA,CAACH,GAAG;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC,eACPX,OAAA,CAACF,cAAc;IACbc,QAAQ,EAAC,WAAW;IACpBC,SAAS,EAAE,IAAK;IAChBC,eAAe,EAAE,KAAM;IACvBC,WAAW,EAAE,KAAM;IACnBC,YAAY;IACZC,GAAG,EAAE,KAAM;IACXC,gBAAgB;IAChBC,SAAS;IACTC,YAAY;EAAA;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACc,CACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}