import React, { useState, useEffect } from 'react';
import { 
  Contain<PERSON>, Row, Col, Card, Button, Form, Alert, Spinner, 
  ProgressBar, Badge, InputGroup 
} from 'react-bootstrap';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { toast } from 'react-toastify';

function EvaluationForm() {
  const navigate = useNavigate();
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    employee_id: '',
    evaluation_period_start: '',
    evaluation_period_end: '',
    total_work_days: '',
    present_days: '',
    late_arrivals: '',
    violations_count: '',
    attendance_score: '',
    punctuality_score: '',
    compliance_score: '',
    notes: ''
  });
  const [calculatedScores, setCalculatedScores] = useState({
    attendance: 0,
    punctuality: 0,
    compliance: 0
  });
  const [autoCalculate, setAutoCalculate] = useState(true);

  useEffect(() => {
    fetchEmployees();
  }, []);

  useEffect(() => {
    if (autoCalculate) {
      calculateScores();
    }
  }, [formData.total_work_days, formData.present_days, formData.late_arrivals, formData.violations_count, autoCalculate]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/employees?per_page=100');
      setEmployees(response.data.employees);
    } catch (err) {
      setError('Gagal memuat data karyawan');
      console.error('Fetch employees error:', err);
    } finally {
      setLoading(false);
    }
  };

  const calculateScores = () => {
    const totalDays = parseInt(formData.total_work_days) || 0;
    const presentDays = parseInt(formData.present_days) || 0;
    const lateArrivals = parseInt(formData.late_arrivals) || 0;
    const violations = parseInt(formData.violations_count) || 0;

    // Calculate attendance score
    const attendanceScore = totalDays > 0 ? Math.min(100, (presentDays / totalDays) * 100) : 0;

    // Calculate punctuality score
    const punctualityScore = presentDays > 0 ? Math.max(0, ((presentDays - lateArrivals) / presentDays) * 100) : 100;

    // Calculate compliance score (assuming max 1 violation per 30 days is acceptable)
    const maxAcceptableViolations = Math.max(1, totalDays / 30);
    let complianceScore = 100;
    
    if (violations > 0) {
      if (violations <= maxAcceptableViolations) {
        complianceScore = 100 - (violations / maxAcceptableViolations) * 30;
      } else {
        const excessViolations = violations - maxAcceptableViolations;
        complianceScore = Math.max(0, 70 - (excessViolations * 15));
      }
    }

    const newScores = {
      attendance: Math.round(attendanceScore * 100) / 100,
      punctuality: Math.round(punctualityScore * 100) / 100,
      compliance: Math.round(complianceScore * 100) / 100
    };

    setCalculatedScores(newScores);

    if (autoCalculate) {
      setFormData(prev => ({
        ...prev,
        attendance_score: newScores.attendance.toString(),
        punctuality_score: newScores.punctuality.toString(),
        compliance_score: newScores.compliance.toString()
      }));
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleScoreChange = (e) => {
    const { name, value } = e.target;
    const numValue = parseFloat(value);
    
    if (numValue < 0 || numValue > 100) {
      return;
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAutoCalculateToggle = (e) => {
    setAutoCalculate(e.target.checked);
    if (e.target.checked) {
      calculateScores();
    }
  };

  const validateForm = () => {
    const requiredFields = [
      'employee_id', 'evaluation_period_start', 'evaluation_period_end',
      'total_work_days', 'present_days', 'late_arrivals', 'violations_count',
      'attendance_score', 'punctuality_score', 'compliance_score'
    ];

    for (const field of requiredFields) {
      if (!formData[field]) {
        return `Field ${field.replace('_', ' ')} harus diisi`;
      }
    }

    // Validate dates
    const startDate = new Date(formData.evaluation_period_start);
    const endDate = new Date(formData.evaluation_period_end);
    
    if (startDate >= endDate) {
      return 'Tanggal mulai harus sebelum tanggal selesai';
    }

    // Validate numeric values
    const totalDays = parseInt(formData.total_work_days);
    const presentDays = parseInt(formData.present_days);
    const lateArrivals = parseInt(formData.late_arrivals);

    if (presentDays > totalDays) {
      return 'Hari hadir tidak boleh lebih dari total hari kerja';
    }

    if (lateArrivals > presentDays) {
      return 'Jumlah terlambat tidak boleh lebih dari hari hadir';
    }

    // Validate scores
    const scores = [
      parseFloat(formData.attendance_score),
      parseFloat(formData.punctuality_score),
      parseFloat(formData.compliance_score)
    ];

    for (const score of scores) {
      if (score < 0 || score > 100) {
        return 'Skor harus antara 0-100';
      }
    }

    return null;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    try {
      setSubmitting(true);
      
      const submitData = {
        ...formData,
        total_work_days: parseInt(formData.total_work_days),
        present_days: parseInt(formData.present_days),
        late_arrivals: parseInt(formData.late_arrivals),
        violations_count: parseInt(formData.violations_count),
        attendance_score: parseFloat(formData.attendance_score),
        punctuality_score: parseFloat(formData.punctuality_score),
        compliance_score: parseFloat(formData.compliance_score)
      };

      const response = await axios.post('/api/evaluations', submitData);
      
      toast.success('Evaluasi berhasil dibuat dan diproses dengan fuzzy logic!');
      navigate('/results', { 
        state: { 
          newEvaluationId: response.data.evaluation.id 
        }
      });
      
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Terjadi kesalahan saat menyimpan evaluasi';
      toast.error(errorMessage);
      console.error('Submit evaluation error:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const getScoreColor = (score) => {
    if (score >= 80) return 'success';
    if (score >= 60) return 'info';
    if (score >= 40) return 'warning';
    return 'danger';
  };

  const getScoreLabel = (score) => {
    if (score >= 80) return 'Tinggi';
    if (score >= 60) return 'Sedang';
    if (score >= 40) return 'Cukup';
    return 'Rendah';
  };

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">Form Evaluasi Kedisiplinan</h1>
          <p className="text-muted">Buat evaluasi kedisiplinan karyawan menggunakan fuzzy logic</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      <Form onSubmit={handleSubmit}>
        <Row>
          <Col lg={8}>
            {/* Employee Selection */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">1. Pilih Karyawan</h5>
              </Card.Header>
              <Card.Body>
                <Form.Group>
                  <Form.Label>Karyawan *</Form.Label>
                  <Form.Select
                    name="employee_id"
                    value={formData.employee_id}
                    onChange={handleChange}
                    required
                  >
                    <option value="">Pilih karyawan...</option>
                    {employees.map(employee => (
                      <option key={employee.id} value={employee.id}>
                        {employee.employee_id} - {employee.full_name} ({employee.department})
                      </option>
                    ))}
                  </Form.Select>
                </Form.Group>
              </Card.Body>
            </Card>

            {/* Evaluation Period */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">2. Periode Evaluasi</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Tanggal Mulai *</Form.Label>
                      <Form.Control
                        type="date"
                        name="evaluation_period_start"
                        value={formData.evaluation_period_start}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Tanggal Selesai *</Form.Label>
                      <Form.Control
                        type="date"
                        name="evaluation_period_end"
                        value={formData.evaluation_period_end}
                        onChange={handleChange}
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Attendance Data */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">3. Data Kehadiran</h5>
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Total Hari Kerja *</Form.Label>
                      <Form.Control
                        type="number"
                        name="total_work_days"
                        value={formData.total_work_days}
                        onChange={handleChange}
                        min="1"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Hari Hadir *</Form.Label>
                      <Form.Control
                        type="number"
                        name="present_days"
                        value={formData.present_days}
                        onChange={handleChange}
                        min="0"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Jumlah Terlambat *</Form.Label>
                      <Form.Control
                        type="number"
                        name="late_arrivals"
                        value={formData.late_arrivals}
                        onChange={handleChange}
                        min="0"
                        required
                      />
                    </Form.Group>
                  </Col>
                  <Col md={6}>
                    <Form.Group className="mb-3">
                      <Form.Label>Jumlah Pelanggaran *</Form.Label>
                      <Form.Control
                        type="number"
                        name="violations_count"
                        value={formData.violations_count}
                        onChange={handleChange}
                        min="0"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Scores */}
            <Card className="mb-4">
              <Card.Header className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">4. Skor Evaluasi</h5>
                <Form.Check
                  type="switch"
                  id="auto-calculate"
                  label="Hitung Otomatis"
                  checked={autoCalculate}
                  onChange={handleAutoCalculateToggle}
                />
              </Card.Header>
              <Card.Body>
                <Row>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Skor Kehadiran (0-100) *</Form.Label>
                      <InputGroup>
                        <Form.Control
                          type="number"
                          name="attendance_score"
                          value={formData.attendance_score}
                          onChange={handleScoreChange}
                          min="0"
                          max="100"
                          step="0.01"
                          required
                          disabled={autoCalculate}
                        />
                        <InputGroup.Text>%</InputGroup.Text>
                      </InputGroup>
                      {autoCalculate && (
                        <Form.Text className="text-muted">
                          Dihitung otomatis: {calculatedScores.attendance}%
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Skor Ketepatan Waktu (0-100) *</Form.Label>
                      <InputGroup>
                        <Form.Control
                          type="number"
                          name="punctuality_score"
                          value={formData.punctuality_score}
                          onChange={handleScoreChange}
                          min="0"
                          max="100"
                          step="0.01"
                          required
                          disabled={autoCalculate}
                        />
                        <InputGroup.Text>%</InputGroup.Text>
                      </InputGroup>
                      {autoCalculate && (
                        <Form.Text className="text-muted">
                          Dihitung otomatis: {calculatedScores.punctuality}%
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group className="mb-3">
                      <Form.Label>Skor Kepatuhan (0-100) *</Form.Label>
                      <InputGroup>
                        <Form.Control
                          type="number"
                          name="compliance_score"
                          value={formData.compliance_score}
                          onChange={handleScoreChange}
                          min="0"
                          max="100"
                          step="0.01"
                          required
                          disabled={autoCalculate}
                        />
                        <InputGroup.Text>%</InputGroup.Text>
                      </InputGroup>
                      {autoCalculate && (
                        <Form.Text className="text-muted">
                          Dihitung otomatis: {calculatedScores.compliance}%
                        </Form.Text>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
              </Card.Body>
            </Card>

            {/* Notes */}
            <Card className="mb-4">
              <Card.Header>
                <h5 className="mb-0">5. Catatan Tambahan</h5>
              </Card.Header>
              <Card.Body>
                <Form.Group>
                  <Form.Label>Catatan</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={3}
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    placeholder="Tambahkan catatan evaluasi (opsional)..."
                  />
                </Form.Group>
              </Card.Body>
            </Card>

            {/* Submit Button */}
            <div className="d-flex justify-content-end gap-2">
              <Button
                variant="secondary"
                onClick={() => navigate('/dashboard')}
                disabled={submitting}
              >
                Batal
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={submitting}
              >
                {submitting ? (
                  <>
                    <Spinner
                      as="span"
                      animation="border"
                      size="sm"
                      role="status"
                      aria-hidden="true"
                      className="me-2"
                    />
                    Memproses...
                  </>
                ) : (
                  'Buat Evaluasi'
                )}
              </Button>
            </div>
          </Col>

          {/* Score Preview */}
          <Col lg={4}>
            <Card className="sticky-top" style={{ top: '20px' }}>
              <Card.Header>
                <h5 className="mb-0">Preview Skor</h5>
              </Card.Header>
              <Card.Body>
                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>Kehadiran</span>
                    <Badge bg={getScoreColor(parseFloat(formData.attendance_score) || 0)}>
                      {getScoreLabel(parseFloat(formData.attendance_score) || 0)}
                    </Badge>
                  </div>
                  <ProgressBar
                    variant={getScoreColor(parseFloat(formData.attendance_score) || 0)}
                    now={parseFloat(formData.attendance_score) || 0}
                    label={`${formData.attendance_score || 0}%`}
                  />
                </div>

                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>Ketepatan Waktu</span>
                    <Badge bg={getScoreColor(parseFloat(formData.punctuality_score) || 0)}>
                      {getScoreLabel(parseFloat(formData.punctuality_score) || 0)}
                    </Badge>
                  </div>
                  <ProgressBar
                    variant={getScoreColor(parseFloat(formData.punctuality_score) || 0)}
                    now={parseFloat(formData.punctuality_score) || 0}
                    label={`${formData.punctuality_score || 0}%`}
                  />
                </div>

                <div className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <span>Kepatuhan</span>
                    <Badge bg={getScoreColor(parseFloat(formData.compliance_score) || 0)}>
                      {getScoreLabel(parseFloat(formData.compliance_score) || 0)}
                    </Badge>
                  </div>
                  <ProgressBar
                    variant={getScoreColor(parseFloat(formData.compliance_score) || 0)}
                    now={parseFloat(formData.compliance_score) || 0}
                    label={`${formData.compliance_score || 0}%`}
                  />
                </div>

                <hr />
                <div className="text-center">
                  <small className="text-muted">
                    Hasil fuzzy logic akan ditampilkan setelah evaluasi dibuat
                  </small>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Form>
    </Container>
  );
}

export default EvaluationForm;
