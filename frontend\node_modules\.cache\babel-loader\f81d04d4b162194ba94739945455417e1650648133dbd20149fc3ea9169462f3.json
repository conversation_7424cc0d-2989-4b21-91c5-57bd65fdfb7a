{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nexport function isAccordionItemSelected(activeEventKey, eventKey) {\n  return Array.isArray(activeEventKey) ? activeEventKey.includes(eventKey) : activeEventKey === eventKey;\n}\nconst context = /*#__PURE__*/React.createContext({});\ncontext.displayName = 'AccordionContext';\nexport default context;", "map": {"version": 3, "names": ["React", "isAccordionItemSelected", "activeEventKey", "eventKey", "Array", "isArray", "includes", "context", "createContext", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/AccordionContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nexport function isAccordionItemSelected(activeEventKey, eventKey) {\n  return Array.isArray(activeEventKey) ? activeEventKey.includes(eventKey) : activeEventKey === eventKey;\n}\nconst context = /*#__PURE__*/React.createContext({});\ncontext.displayName = 'AccordionContext';\nexport default context;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,uBAAuBA,CAACC,cAAc,EAAEC,QAAQ,EAAE;EAChE,OAAOC,KAAK,CAACC,OAAO,CAACH,cAAc,CAAC,GAAGA,cAAc,CAACI,QAAQ,CAACH,QAAQ,CAAC,GAAGD,cAAc,KAAKC,QAAQ;AACxG;AACA,MAAMI,OAAO,GAAG,aAAaP,KAAK,CAACQ,aAAa,CAAC,CAAC,CAAC,CAAC;AACpDD,OAAO,CAACE,WAAW,GAAG,kBAAkB;AACxC,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}