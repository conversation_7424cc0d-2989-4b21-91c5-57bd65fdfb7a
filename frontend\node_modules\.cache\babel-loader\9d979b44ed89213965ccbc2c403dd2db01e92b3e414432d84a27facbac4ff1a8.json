{"ast": null, "code": "import invariant from 'invariant';\nimport { useCallback } from 'react';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nexport default function useWrappedRefWithWarning(ref, componentName) {\n  // @ts-expect-error Ignore global __DEV__ variable\n  if (!(process.env.NODE_ENV !== \"production\")) return ref;\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const warningRef = useCallback(refValue => {\n    !(refValue == null || !refValue.isReactComponent) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${componentName} injected a ref to a provided \\`as\\` component that resolved to a component instance instead of a DOM element. ` + 'Use `React.forwardRef` to provide the injected ref to the class component as a prop in order to pass it directly to a DOM element') : invariant(false) : void 0;\n  }, [componentName]);\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useMergedRefs(warningRef, ref);\n}", "map": {"version": 3, "names": ["invariant", "useCallback", "useMergedRefs", "useWrappedRefWithWarning", "ref", "componentName", "process", "env", "NODE_ENV", "warningRef", "refValue", "isReactComponent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/useWrappedRefWithWarning.js"], "sourcesContent": ["import invariant from 'invariant';\nimport { useCallback } from 'react';\nimport useMergedRefs from '@restart/hooks/useMergedRefs';\nexport default function useWrappedRefWithWarning(ref, componentName) {\n  // @ts-expect-error Ignore global __DEV__ variable\n  if (!(process.env.NODE_ENV !== \"production\")) return ref;\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const warningRef = useCallback(refValue => {\n    !(refValue == null || !refValue.isReactComponent) ? process.env.NODE_ENV !== \"production\" ? invariant(false, `${componentName} injected a ref to a provided \\`as\\` component that resolved to a component instance instead of a DOM element. ` + 'Use `React.forwardRef` to provide the injected ref to the class component as a prop in order to pass it directly to a DOM element') : invariant(false) : void 0;\n  }, [componentName]);\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useMergedRefs(warningRef, ref);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,WAAW;AACjC,SAASC,WAAW,QAAQ,OAAO;AACnC,OAAOC,aAAa,MAAM,8BAA8B;AACxD,eAAe,SAASC,wBAAwBA,CAACC,GAAG,EAAEC,aAAa,EAAE;EACnE;EACA,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,CAAC,EAAE,OAAOJ,GAAG;;EAExD;EACA,MAAMK,UAAU,GAAGR,WAAW,CAACS,QAAQ,IAAI;IACzC,EAAEA,QAAQ,IAAI,IAAI,IAAI,CAACA,QAAQ,CAACC,gBAAgB,CAAC,GAAGL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,SAAS,CAAC,KAAK,EAAE,GAAGK,aAAa,iHAAiH,GAAG,mIAAmI,CAAC,GAAGL,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;EACnZ,CAAC,EAAE,CAACK,aAAa,CAAC,CAAC;EACnB;EACA,OAAOH,aAAa,CAACO,UAAU,EAAEL,GAAG,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}