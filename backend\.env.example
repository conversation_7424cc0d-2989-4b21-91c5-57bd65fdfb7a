# Database Configuration
DATABASE_URL=mysql://username:password@localhost/fuzzy_discipline_db
# Alternative for PostgreSQL:
# DATABASE_URL=postgresql://username:password@localhost/fuzzy_discipline_db

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database Settings
DB_HOST=localhost
DB_PORT=3306
DB_NAME=fuzzy_discipline_db
DB_USER=your_username
DB_PASSWORD=your_password

# Application Settings
DEBUG=True
PORT=5000
