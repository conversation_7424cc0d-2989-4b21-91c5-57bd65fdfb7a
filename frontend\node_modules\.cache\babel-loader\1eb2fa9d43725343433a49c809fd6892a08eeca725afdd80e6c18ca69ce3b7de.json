{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarToggle = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  label = 'Toggle navigation',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'button',\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-toggler');\n  const {\n    onToggle,\n    expanded\n  } = useContext(NavbarContext) || {};\n  const handleClick = useEventCallback(e => {\n    if (onClick) onClick(e);\n    if (onToggle) onToggle();\n  });\n  if (Component === 'button') {\n    props.type = 'button';\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    onClick: handleClick,\n    \"aria-label\": label,\n    className: classNames(className, bsPrefix, !expanded && 'collapsed'),\n    children: children || /*#__PURE__*/_jsx(\"span\", {\n      className: `${bsPrefix}-icon`\n    })\n  });\n});\nNavbarToggle.displayName = 'NavbarToggle';\nexport default NavbarToggle;", "map": {"version": 3, "names": ["classNames", "React", "useContext", "useEventCallback", "useBootstrapPrefix", "NavbarContext", "jsx", "_jsx", "Navbar<PERSON><PERSON><PERSON>", "forwardRef", "bsPrefix", "className", "children", "label", "as", "Component", "onClick", "props", "ref", "onToggle", "expanded", "handleClick", "e", "type", "displayName"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/NavbarToggle.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarToggle = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  children,\n  label = 'Toggle navigation',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'button',\n  onClick,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'navbar-toggler');\n  const {\n    onToggle,\n    expanded\n  } = useContext(NavbarContext) || {};\n  const handleClick = useEventCallback(e => {\n    if (onClick) onClick(e);\n    if (onToggle) onToggle();\n  });\n  if (Component === 'button') {\n    props.type = 'button';\n  }\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    ref: ref,\n    onClick: handleClick,\n    \"aria-label\": label,\n    className: classNames(className, bsPrefix, !expanded && 'collapsed'),\n    children: children || /*#__PURE__*/_jsx(\"span\", {\n      className: `${bsPrefix}-icon`\n    })\n  });\n});\nNavbarToggle.displayName = 'NavbarToggle';\nexport default NavbarToggle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,YAAY,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EAClDC,QAAQ;EACRC,SAAS;EACTC,QAAQ;EACRC,KAAK,GAAG,mBAAmB;EAC3B;EACAC,EAAE,EAAEC,SAAS,GAAG,QAAQ;EACxBC,OAAO;EACP,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTR,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,gBAAgB,CAAC;EACzD,MAAM;IACJS,QAAQ;IACRC;EACF,CAAC,GAAGlB,UAAU,CAACG,aAAa,CAAC,IAAI,CAAC,CAAC;EACnC,MAAMgB,WAAW,GAAGlB,gBAAgB,CAACmB,CAAC,IAAI;IACxC,IAAIN,OAAO,EAAEA,OAAO,CAACM,CAAC,CAAC;IACvB,IAAIH,QAAQ,EAAEA,QAAQ,CAAC,CAAC;EAC1B,CAAC,CAAC;EACF,IAAIJ,SAAS,KAAK,QAAQ,EAAE;IAC1BE,KAAK,CAACM,IAAI,GAAG,QAAQ;EACvB;EACA,OAAO,aAAahB,IAAI,CAACQ,SAAS,EAAE;IAClC,GAAGE,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRF,OAAO,EAAEK,WAAW;IACpB,YAAY,EAAER,KAAK;IACnBF,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAED,QAAQ,EAAE,CAACU,QAAQ,IAAI,WAAW,CAAC;IACpER,QAAQ,EAAEA,QAAQ,IAAI,aAAaL,IAAI,CAAC,MAAM,EAAE;MAC9CI,SAAS,EAAE,GAAGD,QAAQ;IACxB,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFF,YAAY,CAACgB,WAAW,GAAG,cAAc;AACzC,eAAehB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}