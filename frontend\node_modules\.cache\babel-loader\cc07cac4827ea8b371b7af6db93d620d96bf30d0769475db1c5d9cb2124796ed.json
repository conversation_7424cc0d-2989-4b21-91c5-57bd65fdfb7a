{"ast": null, "code": "import * as React from 'react';\nexport function isEsc<PERSON>ey(e) {\n  return e.code === 'Escape' || e.keyCode === 27;\n}\nexport function getReactVersion() {\n  const parts = React.version.split('.');\n  return {\n    major: +parts[0],\n    minor: +parts[1],\n    patch: +parts[2]\n  };\n}\nexport function getChildRef(element) {\n  if (!element || typeof element === 'function') {\n    return null;\n  }\n  const {\n    major\n  } = getReactVersion();\n  const childRef = major >= 19 ? element.props.ref : element.ref;\n  return childRef;\n}", "map": {"version": 3, "names": ["React", "isEscKey", "e", "code", "keyCode", "getReactVersion", "parts", "version", "split", "major", "minor", "patch", "getChildRef", "element", "childRef", "props", "ref"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/@restart/ui/esm/utils.js"], "sourcesContent": ["import * as React from 'react';\nexport function isEsc<PERSON>ey(e) {\n  return e.code === 'Escape' || e.keyCode === 27;\n}\nexport function getReactVersion() {\n  const parts = React.version.split('.');\n  return {\n    major: +parts[0],\n    minor: +parts[1],\n    patch: +parts[2]\n  };\n}\nexport function getChildRef(element) {\n  if (!element || typeof element === 'function') {\n    return null;\n  }\n  const {\n    major\n  } = getReactVersion();\n  const childRef = major >= 19 ? element.props.ref : element.ref;\n  return childRef;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,SAASC,QAAQA,CAACC,CAAC,EAAE;EAC1B,OAAOA,CAAC,CAACC,IAAI,KAAK,QAAQ,IAAID,CAAC,CAACE,OAAO,KAAK,EAAE;AAChD;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,KAAK,GAAGN,KAAK,CAACO,OAAO,CAACC,KAAK,CAAC,GAAG,CAAC;EACtC,OAAO;IACLC,KAAK,EAAE,CAACH,KAAK,CAAC,CAAC,CAAC;IAChBI,KAAK,EAAE,CAACJ,KAAK,CAAC,CAAC,CAAC;IAChBK,KAAK,EAAE,CAACL,KAAK,CAAC,CAAC;EACjB,CAAC;AACH;AACA,OAAO,SAASM,WAAWA,CAACC,OAAO,EAAE;EACnC,IAAI,CAACA,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;IAC7C,OAAO,IAAI;EACb;EACA,MAAM;IACJJ;EACF,CAAC,GAAGJ,eAAe,CAAC,CAAC;EACrB,MAAMS,QAAQ,GAAGL,KAAK,IAAI,EAAE,GAAGI,OAAO,CAACE,KAAK,CAACC,GAAG,GAAGH,OAAO,CAACG,GAAG;EAC9D,OAAOF,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}