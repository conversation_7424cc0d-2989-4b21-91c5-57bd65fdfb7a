-- Fuzzy Logic Employee Discipline Evaluation System Database Schema

-- Create database
CREATE DATABASE IF NOT EXISTS fuzzy_discipline_db;
USE fuzzy_discipline_db;

-- Users table (Admin/HRD)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'hrd') DEFAULT 'hrd',
    full_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Employees table
CREATE TABLE employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id VARCHAR(20) UNIQUE NOT NULL,
    full_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    department VARCHAR(50) NOT NULL,
    position VARCHAR(50) NOT NULL,
    hire_date DATE NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Evaluations table (Input data for fuzzy logic)
CREATE TABLE evaluations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employee_id INT NOT NULL,
    evaluator_id INT NOT NULL,
    evaluation_period_start DATE NOT NULL,
    evaluation_period_end DATE NOT NULL,
    
    -- Input criteria (0-100 scale)
    attendance_score DECIMAL(5,2) NOT NULL, -- Kehadiran
    punctuality_score DECIMAL(5,2) NOT NULL, -- Ketepatan Waktu  
    compliance_score DECIMAL(5,2) NOT NULL, -- Kepatuhan
    
    -- Additional details
    total_work_days INT NOT NULL,
    present_days INT NOT NULL,
    late_arrivals INT NOT NULL,
    violations_count INT NOT NULL,
    notes TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    FOREIGN KEY (evaluator_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Evaluation results table (Fuzzy logic output)
CREATE TABLE evaluation_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    evaluation_id INT NOT NULL,
    
    -- Fuzzy logic intermediate values
    attendance_fuzzy_low DECIMAL(5,4),
    attendance_fuzzy_medium DECIMAL(5,4),
    attendance_fuzzy_high DECIMAL(5,4),
    
    punctuality_fuzzy_low DECIMAL(5,4),
    punctuality_fuzzy_medium DECIMAL(5,4),
    punctuality_fuzzy_high DECIMAL(5,4),
    
    compliance_fuzzy_low DECIMAL(5,4),
    compliance_fuzzy_medium DECIMAL(5,4),
    compliance_fuzzy_high DECIMAL(5,4),
    
    -- Final results
    discipline_score DECIMAL(5,2) NOT NULL, -- Crisp output (0-100)
    discipline_level ENUM('Kurang', 'Cukup', 'Disiplin', 'Sangat Disiplin') NOT NULL,
    confidence_level DECIMAL(5,2), -- Confidence in the result
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (evaluation_id) REFERENCES evaluations(id) ON DELETE CASCADE
);

-- Reports table (Generated reports)
CREATE TABLE reports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    evaluation_id INT NOT NULL,
    generated_by INT NOT NULL,
    report_type ENUM('individual', 'department', 'summary') DEFAULT 'individual',
    file_path VARCHAR(255),
    file_name VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (evaluation_id) REFERENCES evaluations(id) ON DELETE CASCADE,
    FOREIGN KEY (generated_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, role, full_name) VALUES 
('admin', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3bp.Gm.F5u', 'admin', 'System Administrator');

-- Create indexes for better performance
CREATE INDEX idx_employees_employee_id ON employees(employee_id);
CREATE INDEX idx_evaluations_employee_id ON evaluations(employee_id);
CREATE INDEX idx_evaluations_period ON evaluations(evaluation_period_start, evaluation_period_end);
CREATE INDEX idx_evaluation_results_evaluation_id ON evaluation_results(evaluation_id);
CREATE INDEX idx_reports_evaluation_id ON reports(evaluation_id);
