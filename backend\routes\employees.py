from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models.user import User
from models.employee import Employee, db
from datetime import datetime

employees_bp = Blueprint('employees', __name__)

@employees_bp.route('', methods=['GET'])
@jwt_required()
def get_employees():
    """Get all employees with optional filtering"""
    try:
        # Query parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        search = request.args.get('search', '')
        department = request.args.get('department', '')
        is_active = request.args.get('is_active', 'true').lower() == 'true'
        
        # Build query
        query = Employee.query.filter_by(is_active=is_active)
        
        if search:
            query = query.filter(
                db.or_(
                    Employee.full_name.contains(search),
                    Employee.employee_id.contains(search),
                    Employee.position.contains(search)
                )
            )
        
        if department:
            query = query.filter_by(department=department)
        
        # Paginate results
        employees = query.order_by(Employee.full_name).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'employees': [emp.to_dict() for emp in employees.items],
            'pagination': {
                'page': employees.page,
                'pages': employees.pages,
                'per_page': employees.per_page,
                'total': employees.total,
                'has_next': employees.has_next,
                'has_prev': employees.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/<int:employee_id>', methods=['GET'])
@jwt_required()
def get_employee(employee_id):
    """Get specific employee by ID"""
    try:
        employee = Employee.query.get(employee_id)
        
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404
        
        return jsonify(employee.to_dict()), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@employees_bp.route('', methods=['POST'])
@jwt_required()
def create_employee():
    """Create new employee"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # Check if user has permission (admin or hrd)
        if user.role not in ['admin', 'hrd']:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['employee_id', 'full_name', 'department', 'position', 'hire_date']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'{field} is required'}), 400
        
        # Check if employee_id already exists
        existing_employee = Employee.query.filter_by(employee_id=data['employee_id']).first()
        if existing_employee:
            return jsonify({'error': 'Employee ID already exists'}), 400
        
        # Parse hire_date
        try:
            hire_date = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid hire_date format. Use YYYY-MM-DD'}), 400
        
        # Create new employee
        employee = Employee(
            employee_id=data['employee_id'],
            full_name=data['full_name'],
            department=data['department'],
            position=data['position'],
            hire_date=hire_date,
            email=data.get('email'),
            phone=data.get('phone')
        )
        
        db.session.add(employee)
        db.session.commit()
        
        return jsonify({
            'message': 'Employee created successfully',
            'employee': employee.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/<int:employee_id>', methods=['PUT'])
@jwt_required()
def update_employee(employee_id):
    """Update employee information"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # Check if user has permission (admin or hrd)
        if user.role not in ['admin', 'hrd']:
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404
        
        data = request.get_json()
        
        # Update fields
        if 'employee_id' in data:
            # Check if new employee_id already exists
            existing_employee = Employee.query.filter_by(employee_id=data['employee_id']).first()
            if existing_employee and existing_employee.id != employee.id:
                return jsonify({'error': 'Employee ID already exists'}), 400
            employee.employee_id = data['employee_id']
        
        if 'full_name' in data:
            employee.full_name = data['full_name']
        if 'department' in data:
            employee.department = data['department']
        if 'position' in data:
            employee.position = data['position']
        if 'hire_date' in data:
            try:
                employee.hire_date = datetime.strptime(data['hire_date'], '%Y-%m-%d').date()
            except ValueError:
                return jsonify({'error': 'Invalid hire_date format. Use YYYY-MM-DD'}), 400
        if 'email' in data:
            employee.email = data['email']
        if 'phone' in data:
            employee.phone = data['phone']
        if 'is_active' in data:
            employee.is_active = data['is_active']
        
        db.session.commit()
        
        return jsonify({
            'message': 'Employee updated successfully',
            'employee': employee.to_dict()
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/<int:employee_id>', methods=['DELETE'])
@jwt_required()
def delete_employee(employee_id):
    """Delete employee (soft delete)"""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        # Check if user has permission (admin only)
        if user.role != 'admin':
            return jsonify({'error': 'Insufficient permissions'}), 403
        
        employee = Employee.query.get(employee_id)
        if not employee:
            return jsonify({'error': 'Employee not found'}), 404
        
        # Soft delete
        employee.is_active = False
        db.session.commit()
        
        return jsonify({'message': 'Employee deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@employees_bp.route('/departments', methods=['GET'])
@jwt_required()
def get_departments():
    """Get list of all departments"""
    try:
        departments = db.session.query(Employee.department).filter_by(is_active=True).distinct().all()
        department_list = [dept[0] for dept in departments if dept[0]]
        
        return jsonify({'departments': sorted(department_list)}), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
