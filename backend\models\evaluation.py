from datetime import datetime
from database import db

class Evaluation(db.Model):
    __tablename__ = 'evaluations'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    employee_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('employees.id'), nullable=False)
    evaluator_id = db.Column(db.In<PERSON>ger, db.<PERSON>ey('users.id'), nullable=False)
    evaluation_period_start = db.Column(db.Date, nullable=False)
    evaluation_period_end = db.Column(db.Date, nullable=False)
    
    # Input criteria (0-100 scale)
    attendance_score = db.Column(db.Numeric(5, 2), nullable=False)
    punctuality_score = db.Column(db.Numeric(5, 2), nullable=False)
    compliance_score = db.Column(db.Numeric(5, 2), nullable=False)
    
    # Additional details
    total_work_days = db.Column(db.Integer, nullable=False)
    present_days = db.Column(db.Integer, nullable=False)
    late_arrivals = db.Column(db.Integer, nullable=False)
    violations_count = db.Column(db.Integer, nullable=False)
    notes = db.Column(db.Text)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships will be defined after all models are loaded
    
    def to_dict(self):
        """Convert evaluation object to dictionary"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'employee_name': self.employee.full_name if self.employee else None,
            'employee_employee_id': self.employee.employee_id if self.employee else None,
            'evaluator_id': self.evaluator_id,
            'evaluator_name': self.evaluator.full_name if self.evaluator else None,
            'evaluation_period_start': self.evaluation_period_start.isoformat() if self.evaluation_period_start else None,
            'evaluation_period_end': self.evaluation_period_end.isoformat() if self.evaluation_period_end else None,
            'attendance_score': float(self.attendance_score) if self.attendance_score else None,
            'punctuality_score': float(self.punctuality_score) if self.punctuality_score else None,
            'compliance_score': float(self.compliance_score) if self.compliance_score else None,
            'total_work_days': self.total_work_days,
            'present_days': self.present_days,
            'late_arrivals': self.late_arrivals,
            'violations_count': self.violations_count,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'result': self.result.to_dict() if self.result else None
        }
    
    def calculate_attendance_percentage(self):
        """Calculate attendance percentage"""
        if self.total_work_days == 0:
            return 0
        return (self.present_days / self.total_work_days) * 100
    
    def calculate_punctuality_percentage(self):
        """Calculate punctuality percentage"""
        if self.present_days == 0:
            return 100
        late_percentage = (self.late_arrivals / self.present_days) * 100
        return max(0, 100 - late_percentage)
    
    def __repr__(self):
        return f'<Evaluation {self.id}: {self.employee.full_name if self.employee else "Unknown"} ({self.evaluation_period_start} - {self.evaluation_period_end})>'
