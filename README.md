# Fuzzy Logic Employee Discipline Evaluation System

A comprehensive web-based system for evaluating employee discipline using fuzzy logic algorithms.

## 🎯 System Overview

This system evaluates employee discipline based on three key criteria:
- **<PERSON><PERSON><PERSON><PERSON> (Attendance)**: Employee attendance rate
- **Ke<PERSON>ata<PERSON> Waktu (Punctuality)**: On-time arrival performance
- **<PERSON><PERSON><PERSON><PERSON> (Compliance)**: Rule adherence and behavior compliance

## 🏗️ Architecture

```
Frontend (React) ↔ Backend (Flask + Fuzzy Logic) ↔ Database (MySQL/PostgreSQL)
```

## ✨ Features

- **🔐 Admin/HRD Authentication**: Secure JWT-based login system
- **👥 Employee Data Management**: Complete CRUD operations for employee information
- **🧠 Fuzzy Logic Evaluation**: Automated discipline assessment using scikit-fuzzy
- **📊 Results Dashboard**: Interactive visualization of evaluation results
- **📄 PDF Reports**: Professional report generation with ReportLab
- **🎯 Multi-level Output**: Sangat Disiplin / Disiplin / Cukup / Kurang
- **📱 Responsive Design**: Mobile-friendly Bootstrap interface
- **🔍 Advanced Filtering**: Search and filter capabilities
- **📈 Real-time Calculations**: Live score updates during evaluation

## 📁 Project Structure

```
/
├── backend/                 # Flask API server
│   ├── app.py              # Main Flask application
│   ├── models/             # SQLAlchemy database models
│   ├── routes/             # API endpoints (auth, employees, evaluations, reports)
│   ├── fuzzy_logic/        # Fuzzy logic engine with scikit-fuzzy
│   ├── init_db.py          # Database initialization script
│   └── requirements.txt    # Python dependencies
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts (Auth)
│   │   └── App.js         # Main app component
│   ├── public/            # Static assets
│   └── package.json       # Node.js dependencies
├── database/              # Database setup
│   ├── schema.sql         # Database schema
│   └── README.md          # Database setup guide
├── setup.py               # Automated setup script
└── README.md             # This file
```

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)

```bash
# Run the automated setup script
python setup.py
```

This will:
- Check system requirements
- Set up Python virtual environment
- Install all dependencies
- Create configuration files
- Test the fuzzy logic engine
- Create start scripts

### Option 2: Manual Setup

1. **Backend Setup**
   ```bash
   cd backend
   python -m venv venv

   # Windows
   venv\Scripts\activate

   # Linux/Mac
   source venv/bin/activate

   pip install -r requirements.txt
   cp .env.example .env
   # Edit .env with your database credentials
   python init_db.py
   python app.py
   ```

2. **Frontend Setup**
   ```bash
   cd frontend
   npm install
   npm start
   ```

3. **Database Setup**
   - Create MySQL/PostgreSQL database
   - Update connection string in backend/.env
   - Run `python init_db.py` to initialize

## 🧠 Fuzzy Logic System

### Input Variables (0-100 scale)
1. **Attendance (Kehadiran)**: Calculated from present days / total work days
2. **Punctuality (Ketepatan Waktu)**: Based on late arrivals vs present days
3. **Compliance (Kepatuhan)**: Derived from rule violations count

### Membership Functions
Each input has three fuzzy sets:
- **Low**: Triangular function (0-50)
- **Medium**: Triangular function (30-80)
- **High**: Triangular function (70-100)

### Output Categories
- **Kurang (Poor)**: 0-40 points
- **Cukup (Fair)**: 20-70 points
- **Disiplin (Good)**: 60-90 points
- **Sangat Disiplin (Excellent)**: 80-100 points

### Fuzzy Rules Engine
The system uses 25+ carefully crafted fuzzy rules such as:
- IF attendance=HIGH AND punctuality=HIGH AND compliance=HIGH THEN discipline=SANGAT_DISIPLIN
- IF attendance=LOW AND punctuality=LOW AND compliance=LOW THEN discipline=KURANG

## 🔧 Technology Stack

### Backend
- **Framework**: Flask 2.3.3
- **Database**: SQLAlchemy ORM with MySQL/PostgreSQL
- **Fuzzy Logic**: scikit-fuzzy 0.4.2
- **Authentication**: Flask-JWT-Extended
- **PDF Generation**: ReportLab 4.0.4
- **CORS**: Flask-CORS for API access

### Frontend
- **Framework**: React 18.2.0
- **UI Library**: React Bootstrap 2.8.0
- **Routing**: React Router DOM 6.15.0
- **HTTP Client**: Axios 1.5.0
- **Charts**: Chart.js with react-chartjs-2
- **Notifications**: React Toastify

## 📊 Usage Workflow

1. **Login**: Admin/HRD authentication
2. **Employee Management**: Add/edit employee data
3. **Create Evaluation**: Input attendance, punctuality, and compliance data
4. **Fuzzy Processing**: System automatically processes data through fuzzy logic
5. **View Results**: See discipline scores and levels with confidence ratings
6. **Generate Reports**: Create and download PDF reports
7. **Analysis**: Review trends and patterns in discipline evaluations

## 🔑 Default Credentials

After running the setup:
- **Admin**: username `admin`, password `admin123`
- **HRD**: username `hrd`, password `hrd123`

## 📋 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Token verification
- `GET /api/auth/profile` - User profile

### Employees
- `GET /api/employees` - List employees (paginated)
- `POST /api/employees` - Create employee
- `PUT /api/employees/{id}` - Update employee
- `DELETE /api/employees/{id}` - Delete employee

### Evaluations
- `GET /api/evaluations` - List evaluations
- `POST /api/evaluations` - Create evaluation (triggers fuzzy logic)
- `GET /api/evaluations/{id}` - Get evaluation details
- `PUT /api/evaluations/{id}` - Update evaluation

### Reports
- `GET /api/reports` - List reports
- `POST /api/reports/generate` - Generate PDF report
- `GET /api/reports/{id}/download` - Download report

## 🧪 Testing

### Test Fuzzy Logic Engine
```bash
cd backend
python fuzzy_logic/test_engine.py
```

### Test API Endpoints
```bash
# Health check
curl http://localhost:5000/api/health

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

## 🚀 Deployment

### Development
- Backend: `python app.py` (runs on port 5000)
- Frontend: `npm start` (runs on port 3000)

### Production
- Use production WSGI server (Gunicorn)
- Set up reverse proxy (Nginx)
- Configure SSL/TLS
- Use production database
- Build frontend: `npm run build`

## 📈 Performance Features

- **Database Indexing**: Optimized queries with proper indexes
- **Pagination**: Efficient handling of large datasets
- **Caching**: Request caching for better performance
- **Lazy Loading**: Component-based code splitting
- **Real-time Updates**: Live calculation feedback

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt for secure password storage
- **Input Validation**: Comprehensive input sanitization
- **CORS Configuration**: Proper cross-origin resource sharing
- **SQL Injection Prevention**: SQLAlchemy ORM protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the documentation in each component's README
2. Review the troubleshooting sections
3. Create an issue on the repository

## 🎯 Future Enhancements

- [ ] Multi-language support (Indonesian/English)
- [ ] Advanced analytics and reporting
- [ ] Mobile application
- [ ] Real-time notifications
- [ ] Bulk evaluation processing
- [ ] Integration with HR systems
- [ ] Machine learning improvements
- [ ] Advanced visualization charts
