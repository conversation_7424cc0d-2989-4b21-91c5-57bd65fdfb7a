# Database Setup Guide

This directory contains the database schema and setup instructions for the Fuzzy Logic Employee Discipline Evaluation System.

## Prerequisites

### MySQL Setup
1. Install MySQL Server 8.0 or later
2. Create a database user with appropriate privileges
3. Create the database:
   ```sql
   CREATE DATABASE fuzzy_discipline_db;
   ```

### PostgreSQL Setup (Alternative)
1. Install PostgreSQL 12 or later
2. Create a database user with appropriate privileges
3. Create the database:
   ```sql
   CREATE DATABASE fuzzy_discipline_db;
   ```

## Database Schema

The system uses the following main tables:

- **users**: Admin and HRD user accounts
- **employees**: Employee information
- **evaluations**: Evaluation input data (attendance, punctuality, compliance)
- **evaluation_results**: Fuzzy logic processing results
- **reports**: Generated report metadata

## Setup Instructions

### Option 1: Using SQLAlchemy (Recommended)
1. Navigate to the backend directory
2. Copy `.env.example` to `.env` and configure your database settings
3. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```
4. Run the database initialization script:
   ```bash
   python init_db.py
   ```

### Option 2: Manual SQL Setup
1. Connect to your MySQL/PostgreSQL server
2. Run the schema.sql file:
   ```bash
   mysql -u username -p fuzzy_discipline_db < schema.sql
   # OR for PostgreSQL:
   psql -U username -d fuzzy_discipline_db -f schema.sql
   ```

## Default Users

After initialization, the following users will be available:

- **Admin User**
  - Username: `admin`
  - Password: `admin123`
  - Role: Administrator

- **HRD User**
  - Username: `hrd`
  - Password: `hrd123`
  - Role: HR Department

## Environment Variables

Configure the following variables in your `.env` file:

```env
# Database Configuration
DATABASE_URL=mysql://username:password@localhost/fuzzy_discipline_db

# Alternative individual settings
DB_HOST=localhost
DB_PORT=3306
DB_NAME=fuzzy_discipline_db
DB_USER=your_username
DB_PASSWORD=your_password
```

## Sample Data

The initialization script creates 5 sample employees across different departments:
- Ahmad Wijaya (IT - Software Developer)
- Siti Nurhaliza (Finance - Accountant)
- Budi Santoso (Marketing - Marketing Executive)
- Dewi Lestari (HR - HR Specialist)
- Rudi Hermawan (Operations - Operations Manager)

## Troubleshooting

### Connection Issues
- Verify database server is running
- Check connection credentials in `.env` file
- Ensure database exists and user has proper privileges

### Permission Issues
- Grant necessary privileges to database user:
  ```sql
  GRANT ALL PRIVILEGES ON fuzzy_discipline_db.* TO 'username'@'localhost';
  FLUSH PRIVILEGES;
  ```

### Character Encoding
- Ensure database uses UTF-8 encoding for proper Indonesian text support
