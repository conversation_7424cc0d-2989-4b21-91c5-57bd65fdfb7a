#!/usr/bin/env python3
"""
Test script for Fuzzy Logic Engine
"""

import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fuzzy_logic.engine import FuzzyLogicEngine

def test_fuzzy_engine():
    """Test the fuzzy logic engine with various input combinations"""
    
    print("=" * 60)
    print("TESTING FUZZY LOGIC ENGINE FOR EMPLOYEE DISCIPLINE EVALUATION")
    print("=" * 60)
    
    # Initialize the engine
    engine = FuzzyLogicEngine()
    
    # Test cases with expected outcomes
    test_cases = [
        {
            'name': 'Excellent Employee',
            'attendance': 95,
            'punctuality': 90,
            'compliance': 95,
            'expected_level': 'Sangat Disiplin'
        },
        {
            'name': 'Good Employee',
            'attendance': 85,
            'punctuality': 80,
            'compliance': 85,
            'expected_level': 'Disiplin'
        },
        {
            'name': 'Average Employee',
            'attendance': 70,
            'punctuality': 65,
            'compliance': 70,
            'expected_level': 'Disiplin'
        },
        {
            'name': 'Below Average Employee',
            'attendance': 60,
            'punctuality': 55,
            'compliance': 50,
            'expected_level': 'Cukup'
        },
        {
            'name': 'Poor Employee',
            'attendance': 40,
            'punctuality': 35,
            'compliance': 30,
            'expected_level': 'Kurang'
        },
        {
            'name': 'Mixed Performance 1 (High Attendance, Low Others)',
            'attendance': 90,
            'punctuality': 30,
            'compliance': 25,
            'expected_level': 'Kurang'
        },
        {
            'name': 'Mixed Performance 2 (Good Attendance & Punctuality, Poor Compliance)',
            'attendance': 85,
            'punctuality': 80,
            'compliance': 40,
            'expected_level': 'Cukup'
        },
        {
            'name': 'Mixed Performance 3 (Medium All)',
            'attendance': 60,
            'punctuality': 60,
            'compliance': 60,
            'expected_level': 'Disiplin'
        }
    ]
    
    print("\nRunning test cases...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['name']}")
        print("-" * 50)
        
        # Run evaluation
        result = engine.evaluate(
            attendance=test_case['attendance'],
            punctuality=test_case['punctuality'],
            compliance=test_case['compliance']
        )
        
        # Display inputs
        print(f"Input Values:")
        print(f"  Attendance:   {test_case['attendance']}%")
        print(f"  Punctuality:  {test_case['punctuality']}%")
        print(f"  Compliance:   {test_case['compliance']}%")
        
        # Display fuzzy membership values
        print(f"\nFuzzy Membership Values:")
        print(f"  Attendance:   Low={result['fuzzy_values']['attendance']['low']:.3f}, "
              f"Medium={result['fuzzy_values']['attendance']['medium']:.3f}, "
              f"High={result['fuzzy_values']['attendance']['high']:.3f}")
        print(f"  Punctuality:  Low={result['fuzzy_values']['punctuality']['low']:.3f}, "
              f"Medium={result['fuzzy_values']['punctuality']['medium']:.3f}, "
              f"High={result['fuzzy_values']['punctuality']['high']:.3f}")
        print(f"  Compliance:   Low={result['fuzzy_values']['compliance']['low']:.3f}, "
              f"Medium={result['fuzzy_values']['compliance']['medium']:.3f}, "
              f"High={result['fuzzy_values']['compliance']['high']:.3f}")
        
        # Display results
        print(f"\nResults:")
        print(f"  Discipline Score:     {result['discipline_score']}")
        print(f"  Discipline Level:     {result['discipline_level']}")
        print(f"  Confidence Level:     {result['confidence_level']}%")
        print(f"  Expected Level:       {test_case['expected_level']}")
        
        # Check if result matches expectation
        if result['discipline_level'] == test_case['expected_level']:
            print(f"  ✅ PASS - Result matches expectation")
        else:
            print(f"  ❌ FAIL - Expected {test_case['expected_level']}, got {result['discipline_level']}")
        
        print("\n" + "=" * 60 + "\n")
    
    print("Testing completed!")
    
    # Test edge cases
    print("\nTesting Edge Cases:")
    print("-" * 30)
    
    edge_cases = [
        {'name': 'All Zero', 'attendance': 0, 'punctuality': 0, 'compliance': 0},
        {'name': 'All Maximum', 'attendance': 100, 'punctuality': 100, 'compliance': 100},
        {'name': 'Boundary Case 1', 'attendance': 50, 'punctuality': 50, 'compliance': 50},
        {'name': 'Boundary Case 2', 'attendance': 80, 'punctuality': 80, 'compliance': 80},
    ]
    
    for edge_case in edge_cases:
        result = engine.evaluate(
            attendance=edge_case['attendance'],
            punctuality=edge_case['punctuality'],
            compliance=edge_case['compliance']
        )
        
        print(f"{edge_case['name']}: Score={result['discipline_score']}, "
              f"Level={result['discipline_level']}, Confidence={result['confidence_level']}%")
    
    print("\nEdge case testing completed!")

def test_visualization():
    """Test the visualization functionality"""
    print("\nTesting visualization...")
    
    try:
        engine = FuzzyLogicEngine()
        img_string = engine.visualize_membership_functions()
        
        if img_string and len(img_string) > 0:
            print("✅ Visualization generated successfully")
            print(f"   Image data length: {len(img_string)} characters")
        else:
            print("❌ Visualization failed - empty result")
    
    except Exception as e:
        print(f"❌ Visualization failed with error: {str(e)}")

if __name__ == '__main__':
    test_fuzzy_engine()
    test_visualization()
