# Fuzzy Logic Employee Discipline Evaluation System - Backend

Flask-based REST API with fuzzy logic engine for employee discipline evaluation.

## Features

- **Authentication**: JWT-based authentication for admin and HRD users
- **Employee Management**: CRUD operations for employee data
- **Fuzzy Logic Evaluation**: Automated discipline assessment using scikit-fuzzy
- **Report Generation**: PDF report generation with ReportLab
- **RESTful API**: Complete REST API with proper error handling

## Technology Stack

- **Framework**: Flask 2.3.3
- **Database**: MySQL/PostgreSQL with SQLAlchemy ORM
- **Fuzzy Logic**: scikit-fuzzy 0.4.2
- **Authentication**: Flask-JWT-Extended
- **PDF Generation**: ReportLab
- **CORS**: Flask-CORS for frontend integration

## Installation

### Prerequisites

- Python 3.8 or higher
- MySQL or PostgreSQL database
- pip package manager

### Setup Steps

1. **Create Virtual Environment**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your database credentials:
   ```env
   DATABASE_URL=mysql://username:password@localhost/fuzzy_discipline_db
   SECRET_KEY=your-secret-key-here
   JWT_SECRET_KEY=your-jwt-secret-key-here
   ```

4. **Database Setup**
   ```bash
   # Initialize database with sample data
   python init_db.py
   ```

5. **Run Application**
   ```bash
   python app.py
   ```

The API will be available at `http://localhost:5000`

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Verify JWT token
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/change-password` - Change password

### Employees
- `GET /api/employees` - List employees (with pagination and filters)
- `POST /api/employees` - Create new employee
- `GET /api/employees/{id}` - Get employee details
- `PUT /api/employees/{id}` - Update employee
- `DELETE /api/employees/{id}` - Delete employee (soft delete)
- `GET /api/employees/departments` - Get list of departments

### Evaluations
- `GET /api/evaluations` - List evaluations (with pagination and filters)
- `POST /api/evaluations` - Create new evaluation (processes with fuzzy logic)
- `GET /api/evaluations/{id}` - Get evaluation details
- `PUT /api/evaluations/{id}` - Update evaluation (reprocesses with fuzzy logic)
- `DELETE /api/evaluations/{id}` - Delete evaluation
- `GET /api/evaluations/employee/{employee_id}` - Get evaluations for specific employee

### Reports
- `GET /api/reports` - List generated reports
- `POST /api/reports/generate` - Generate PDF report
- `GET /api/reports/{id}` - Get report details
- `GET /api/reports/{id}/download` - Download report file
- `DELETE /api/reports/{id}` - Delete report

### Health Check
- `GET /api/health` - API health check

## Fuzzy Logic System

The system uses three input criteria to evaluate employee discipline:

### Input Variables (0-100 scale)
1. **Attendance (Kehadiran)**: Employee attendance percentage
2. **Punctuality (Ketepatan Waktu)**: On-time arrival percentage
3. **Compliance (Kepatuhan)**: Rule compliance score

### Membership Functions
Each input has three fuzzy sets:
- **Low**: 0-50 (triangular)
- **Medium**: 30-80 (triangular)
- **High**: 70-100 (triangular)

### Output Variable
**Discipline Level** with four categories:
- **Kurang**: 0-40
- **Cukup**: 20-70
- **Disiplin**: 60-90
- **Sangat Disiplin**: 80-100

### Fuzzy Rules
The system uses 25+ fuzzy rules to determine discipline level based on input combinations.

## Testing

### Test Fuzzy Logic Engine
```bash
python fuzzy_logic/test_engine.py
```

### Manual API Testing
```bash
# Health check
curl http://localhost:5000/api/health

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'

# Get employees (requires JWT token)
curl -X GET http://localhost:5000/api/employees \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Database Schema

### Users Table
- Authentication and user management
- Roles: admin, hrd

### Employees Table
- Employee information and details
- Soft delete support

### Evaluations Table
- Evaluation input data
- Links to employee and evaluator

### Evaluation Results Table
- Fuzzy logic processing results
- Stores intermediate fuzzy values and final results

### Reports Table
- Generated report metadata
- File path and download information

## Default Users

After running `init_db.py`:

- **Admin User**
  - Username: `admin`
  - Password: `admin123`
  - Role: Administrator

- **HRD User**
  - Username: `hrd`
  - Password: `hrd123`
  - Role: HR Department

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | Database connection string | - |
| `SECRET_KEY` | Flask secret key | dev-secret-key |
| `JWT_SECRET_KEY` | JWT signing key | jwt-secret-key |
| `DEBUG` | Debug mode | True |
| `PORT` | Server port | 5000 |

### Database Configuration

The system supports both MySQL and PostgreSQL. Configure via `DATABASE_URL`:

```bash
# MySQL
DATABASE_URL=mysql+pymysql://user:pass@localhost/dbname

# PostgreSQL
DATABASE_URL=postgresql://user:pass@localhost/dbname
```

## Error Handling

The API returns consistent error responses:

```json
{
  "error": "Error message description"
}
```

Common HTTP status codes:
- `200`: Success
- `201`: Created
- `400`: Bad Request
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `500`: Internal Server Error

## Security

- JWT-based authentication
- Password hashing with bcrypt
- CORS configuration for frontend integration
- Input validation and sanitization
- SQL injection prevention with SQLAlchemy ORM

## Performance

- Database indexing on frequently queried fields
- Pagination for large datasets
- Efficient fuzzy logic processing
- Optimized SQL queries with proper joins

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database server is running
   - Verify credentials in `.env` file
   - Ensure database exists

2. **Import Errors**
   - Activate virtual environment
   - Install all requirements: `pip install -r requirements.txt`

3. **Permission Errors**
   - Check file permissions for report generation
   - Ensure write access to reports directory

4. **Fuzzy Logic Errors**
   - Verify scikit-fuzzy installation
   - Check input value ranges (0-100)

### Logs

The application logs errors to console. For production, configure proper logging:

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## Development

### Adding New Endpoints

1. Create route in appropriate blueprint file
2. Add authentication decorator if needed
3. Implement input validation
4. Add error handling
5. Update this documentation

### Database Migrations

For schema changes:
1. Modify model files
2. Create migration script
3. Test with sample data
4. Update `init_db.py` if needed

## Production Deployment

### Recommendations

1. Use production WSGI server (Gunicorn, uWSGI)
2. Set up reverse proxy (Nginx)
3. Configure SSL/TLS
4. Use production database
5. Set proper environment variables
6. Enable logging
7. Set up monitoring

### Example Gunicorn Command
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```
