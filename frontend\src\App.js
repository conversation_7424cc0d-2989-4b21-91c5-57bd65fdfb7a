import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Container } from 'react-bootstrap';
import Navigation from './components/Navigation';
import Login from './components/Login';
import Dashboard from './components/Dashboard';
import EmployeeManagement from './components/EmployeeManagement';
import EvaluationForm from './components/EvaluationForm';
import EvaluationResults from './components/EvaluationResults';
import Reports from './components/Reports';
import { AuthProvider, useAuth } from './contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <AppContent />
        </div>
      </Router>
    </AuthProvider>
  );
}

function AppContent() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </Container>
    );
  }

  return (
    <>
      {user && <Navigation />}
      <Container fluid className={user ? "mt-4" : ""}>
        <Routes>
          <Route 
            path="/login" 
            element={user ? <Navigate to="/dashboard" /> : <Login />} 
          />
          <Route 
            path="/dashboard" 
            element={user ? <Dashboard /> : <Navigate to="/login" />} 
          />
          <Route 
            path="/employees" 
            element={user ? <EmployeeManagement /> : <Navigate to="/login" />} 
          />
          <Route 
            path="/evaluation" 
            element={user ? <EvaluationForm /> : <Navigate to="/login" />} 
          />
          <Route 
            path="/results" 
            element={user ? <EvaluationResults /> : <Navigate to="/login" />} 
          />
          <Route 
            path="/reports" 
            element={user ? <Reports /> : <Navigate to="/login" />} 
          />
          <Route 
            path="/" 
            element={user ? <Navigate to="/dashboard" /> : <Navigate to="/login" />} 
          />
        </Routes>
      </Container>
    </>
  );
}

export default App;
