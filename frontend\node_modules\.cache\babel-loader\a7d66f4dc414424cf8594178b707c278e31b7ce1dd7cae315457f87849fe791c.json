{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport warning from 'warning';\nimport Feedback from './Feedback';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormControl = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  type,\n  size,\n  htmlSize,\n  id,\n  className,\n  isValid = false,\n  isInvalid = false,\n  plaintext,\n  readOnly,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'input',\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-control');\n  process.env.NODE_ENV !== \"production\" ? warning(controlId == null || !id, '`controlId` is ignored on `<FormControl>` when `id` is specified.') : void 0;\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    type: type,\n    size: htmlSize,\n    ref: ref,\n    readOnly: readOnly,\n    id: id || controlId,\n    className: classNames(className, plaintext ? `${bsPrefix}-plaintext` : bsPrefix, size && `${bsPrefix}-${size}`, type === 'color' && `${bsPrefix}-color`, isValid && 'is-valid', isInvalid && 'is-invalid')\n  });\n});\nFormControl.displayName = 'FormControl';\nexport default Object.assign(FormControl, {\n  Feedback\n});", "map": {"version": 3, "names": ["classNames", "React", "useContext", "warning", "<PERSON><PERSON><PERSON>", "FormContext", "useBootstrapPrefix", "jsx", "_jsx", "FormControl", "forwardRef", "bsPrefix", "type", "size", "htmlSize", "id", "className", "<PERSON><PERSON><PERSON><PERSON>", "isInvalid", "plaintext", "readOnly", "as", "Component", "props", "ref", "controlId", "process", "env", "NODE_ENV", "displayName", "Object", "assign"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/FormControl.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport warning from 'warning';\nimport Feedback from './Feedback';\nimport FormContext from './FormContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst FormControl = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  type,\n  size,\n  htmlSize,\n  id,\n  className,\n  isValid = false,\n  isInvalid = false,\n  plaintext,\n  readOnly,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'input',\n  ...props\n}, ref) => {\n  const {\n    controlId\n  } = useContext(FormContext);\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'form-control');\n  process.env.NODE_ENV !== \"production\" ? warning(controlId == null || !id, '`controlId` is ignored on `<FormControl>` when `id` is specified.') : void 0;\n  return /*#__PURE__*/_jsx(Component, {\n    ...props,\n    type: type,\n    size: htmlSize,\n    ref: ref,\n    readOnly: readOnly,\n    id: id || controlId,\n    className: classNames(className, plaintext ? `${bsPrefix}-plaintext` : bsPrefix, size && `${bsPrefix}-${size}`, type === 'color' && `${bsPrefix}-color`, isValid && 'is-valid', isInvalid && 'is-invalid')\n  });\n});\nFormControl.displayName = 'FormControl';\nexport default Object.assign(FormControl, {\n  Feedback\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,WAAW,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EACjDC,QAAQ;EACRC,IAAI;EACJC,IAAI;EACJC,QAAQ;EACRC,EAAE;EACFC,SAAS;EACTC,OAAO,GAAG,KAAK;EACfC,SAAS,GAAG,KAAK;EACjBC,SAAS;EACTC,QAAQ;EACR;EACAC,EAAE,EAAEC,SAAS,GAAG,OAAO;EACvB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM;IACJC;EACF,CAAC,GAAGvB,UAAU,CAACG,WAAW,CAAC;EAC3BM,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,cAAc,CAAC;EACvDe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGzB,OAAO,CAACsB,SAAS,IAAI,IAAI,IAAI,CAACV,EAAE,EAAE,mEAAmE,CAAC,GAAG,KAAK,CAAC;EACvJ,OAAO,aAAaP,IAAI,CAACc,SAAS,EAAE;IAClC,GAAGC,KAAK;IACRX,IAAI,EAAEA,IAAI;IACVC,IAAI,EAAEC,QAAQ;IACdU,GAAG,EAAEA,GAAG;IACRJ,QAAQ,EAAEA,QAAQ;IAClBL,EAAE,EAAEA,EAAE,IAAIU,SAAS;IACnBT,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEG,SAAS,GAAG,GAAGR,QAAQ,YAAY,GAAGA,QAAQ,EAAEE,IAAI,IAAI,GAAGF,QAAQ,IAAIE,IAAI,EAAE,EAAED,IAAI,KAAK,OAAO,IAAI,GAAGD,QAAQ,QAAQ,EAAEM,OAAO,IAAI,UAAU,EAAEC,SAAS,IAAI,YAAY;EAC3M,CAAC,CAAC;AACJ,CAAC,CAAC;AACFT,WAAW,CAACoB,WAAW,GAAG,aAAa;AACvC,eAAeC,MAAM,CAACC,MAAM,CAACtB,WAAW,EAAE;EACxCL;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}