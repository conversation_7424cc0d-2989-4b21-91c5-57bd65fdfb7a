{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { useCol } from './Col';\nexport default function usePlaceholder({\n  animation,\n  bg,\n  bsPrefix,\n  size,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'placeholder');\n  const [{\n    className,\n    ...colProps\n  }] = useCol(props);\n  return {\n    ...colProps,\n    className: classNames(className, animation ? `${bsPrefix}-${animation}` : bsPrefix, size && `${bsPrefix}-${size}`, bg && `bg-${bg}`)\n  };\n}", "map": {"version": 3, "names": ["classNames", "useBootstrapPrefix", "useCol", "usePlaceholder", "animation", "bg", "bsPrefix", "size", "props", "className", "colProps"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/FUZYY LOGIC/frontend/node_modules/react-bootstrap/esm/usePlaceholder.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { useCol } from './Col';\nexport default function usePlaceholder({\n  animation,\n  bg,\n  bsPrefix,\n  size,\n  ...props\n}) {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'placeholder');\n  const [{\n    className,\n    ...colProps\n  }] = useCol(props);\n  return {\n    ...colProps,\n    className: classNames(className, animation ? `${bsPrefix}-${animation}` : bsPrefix, size && `${bsPrefix}-${size}`, bg && `bg-${bg}`)\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,MAAM,QAAQ,OAAO;AAC9B,eAAe,SAASC,cAAcA,CAAC;EACrCC,SAAS;EACTC,EAAE;EACFC,QAAQ;EACRC,IAAI;EACJ,GAAGC;AACL,CAAC,EAAE;EACDF,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,aAAa,CAAC;EACtD,MAAM,CAAC;IACLG,SAAS;IACT,GAAGC;EACL,CAAC,CAAC,GAAGR,MAAM,CAACM,KAAK,CAAC;EAClB,OAAO;IACL,GAAGE,QAAQ;IACXD,SAAS,EAAET,UAAU,CAACS,SAAS,EAAEL,SAAS,GAAG,GAAGE,QAAQ,IAAIF,SAAS,EAAE,GAAGE,QAAQ,EAAEC,IAAI,IAAI,GAAGD,QAAQ,IAAIC,IAAI,EAAE,EAAEF,EAAE,IAAI,MAAMA,EAAE,EAAE;EACrI,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}