#!/usr/bin/env python3
"""
Database initialization script for Fuzzy Logic Employee Discipline Evaluation System
"""

import os
import sys
from datetime import datetime, date
from dotenv import load_dotenv

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

from app import app, db
from models.user import User
from models.employee import Employee
from models.evaluation import Evaluation
from models.evaluation_result import EvaluationResult
from models.report import Report

def init_database():
    """Initialize database with tables and sample data"""
    
    with app.app_context():
        print("Creating database tables...")
        
        # Drop all tables (be careful in production!)
        db.drop_all()
        
        # Create all tables
        db.create_all()
        
        print("Tables created successfully!")
        
        # Create default admin user
        print("Creating default admin user...")
        admin_user = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            full_name='System Administrator'
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)
        
        # Create sample HRD user
        hrd_user = User(
            username='hrd',
            email='<EMAIL>',
            role='hrd',
            full_name='HR Manager'
        )
        hrd_user.set_password('hrd123')
        db.session.add(hrd_user)
        
        # Create sample employees
        print("Creating sample employees...")
        employees_data = [
            {
                'employee_id': 'EMP001',
                'full_name': 'Ahmad Wijaya',
                'department': 'IT',
                'position': 'Software Developer',
                'hire_date': date(2022, 1, 15),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP002',
                'full_name': 'Siti Nurhaliza',
                'department': 'Finance',
                'position': 'Accountant',
                'hire_date': date(2021, 6, 10),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP003',
                'full_name': 'Budi Santoso',
                'department': 'Marketing',
                'position': 'Marketing Executive',
                'hire_date': date(2023, 3, 20),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP004',
                'full_name': 'Dewi Lestari',
                'department': 'HR',
                'position': 'HR Specialist',
                'hire_date': date(2020, 9, 5),
                'email': '<EMAIL>',
                'phone': '************'
            },
            {
                'employee_id': 'EMP005',
                'full_name': 'Rudi Hermawan',
                'department': 'Operations',
                'position': 'Operations Manager',
                'hire_date': date(2019, 11, 12),
                'email': '<EMAIL>',
                'phone': '081234567894'
            }
        ]
        
        for emp_data in employees_data:
            employee = Employee(**emp_data)
            db.session.add(employee)
        
        # Commit the changes
        db.session.commit()
        
        print("Sample data created successfully!")
        print("\nDefault login credentials:")
        print("Admin - Username: admin, Password: admin123")
        print("HRD - Username: hrd, Password: hrd123")
        print("\nDatabase initialization completed!")

if __name__ == '__main__':
    init_database()
