import React, { useState, useEffect } from 'react';
import { 
  Con<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Table, Badge, Spinner, 
  Al<PERSON>, InputGroup, Form 
} from 'react-bootstrap';
import axios from 'axios';
import { toast } from 'react-toastify';

function Reports() {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [pagination, setPagination] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState({
    search: '',
    report_type: ''
  });

  useEffect(() => {
    fetchReports();
  }, [currentPage, filters]);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: 10,
        ...filters
      };
      
      const response = await axios.get('/api/reports', { params });
      setReports(response.data.reports);
      setPagination(response.data.pagination);
    } catch (err) {
      setError('Gagal memuat data laporan');
      console.error('Fetch reports error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (reportId, fileName) => {
    try {
      const response = await axios.get(`/api/reports/${reportId}/download`, {
        responseType: 'blob'
      });
      
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      toast.success('Laporan berhasil diunduh!');
    } catch (err) {
      const errorMessage = err.response?.data?.error || 'Gagal mengunduh laporan';
      toast.error(errorMessage);
      console.error('Download report error:', err);
    }
  };

  const handleDelete = async (reportId, fileName) => {
    if (window.confirm(`Apakah Anda yakin ingin menghapus laporan "${fileName}"?`)) {
      try {
        await axios.delete(`/api/reports/${reportId}`);
        toast.success('Laporan berhasil dihapus');
        fetchReports();
      } catch (err) {
        const errorMessage = err.response?.data?.error || 'Gagal menghapus laporan';
        toast.error(errorMessage);
        console.error('Delete report error:', err);
      }
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: value
    }));
    setCurrentPage(1);
  };

  const getReportTypeBadge = (type) => {
    switch (type) {
      case 'individual':
        return <Badge bg="primary">Individual</Badge>;
      case 'department':
        return <Badge bg="info">Departemen</Badge>;
      case 'summary':
        return <Badge bg="success">Ringkasan</Badge>;
      default:
        return <Badge bg="secondary">{type}</Badge>;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading && reports.length === 0) {
    return (
      <Container className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <Spinner animation="border" role="status">
          <span className="visually-hidden">Loading...</span>
        </Spinner>
      </Container>
    );
  }

  return (
    <Container fluid>
      <Row className="mb-4">
        <Col>
          <h1 className="h3 mb-0">Laporan Evaluasi</h1>
          <p className="text-muted">Kelola dan unduh laporan evaluasi kedisiplinan</p>
        </Col>
      </Row>

      {error && (
        <Alert variant="danger" className="mb-4">
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <div className="display-6 text-primary mb-2">
                <i className="bi bi-file-earmark-pdf-fill"></i>
              </div>
              <h5>Total Laporan</h5>
              <h3 className="text-primary">{pagination.total || 0}</h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <div className="display-6 text-info mb-2">
                <i className="bi bi-person-fill"></i>
              </div>
              <h5>Individual</h5>
              <h3 className="text-info">
                {reports.filter(r => r.report_type === 'individual').length}
              </h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <div className="display-6 text-success mb-2">
                <i className="bi bi-building"></i>
              </div>
              <h5>Departemen</h5>
              <h3 className="text-success">
                {reports.filter(r => r.report_type === 'department').length}
              </h3>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <div className="display-6 text-warning mb-2">
                <i className="bi bi-graph-up"></i>
              </div>
              <h5>Ringkasan</h5>
              <h3 className="text-warning">
                {reports.filter(r => r.report_type === 'summary').length}
              </h3>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Card>
        <Card.Header>
          <Row className="align-items-center">
            <Col>
              <h5 className="mb-0">Daftar Laporan</h5>
            </Col>
          </Row>
        </Card.Header>
        
        <Card.Body>
          {/* Filters */}
          <Row className="mb-3">
            <Col md={6}>
              <InputGroup>
                <InputGroup.Text>
                  <i className="bi bi-search"></i>
                </InputGroup.Text>
                <Form.Control
                  type="text"
                  placeholder="Cari laporan..."
                  name="search"
                  value={filters.search}
                  onChange={handleFilterChange}
                />
              </InputGroup>
            </Col>
            <Col md={3}>
              <Form.Select name="report_type" value={filters.report_type} onChange={handleFilterChange}>
                <option value="">Semua Jenis</option>
                <option value="individual">Individual</option>
                <option value="department">Departemen</option>
                <option value="summary">Ringkasan</option>
              </Form.Select>
            </Col>
          </Row>

          {/* Reports Table */}
          <Table responsive hover>
            <thead>
              <tr>
                <th>Nama File</th>
                <th>Jenis</th>
                <th>Karyawan</th>
                <th>Periode Evaluasi</th>
                <th>Dibuat Oleh</th>
                <th>Tanggal Dibuat</th>
                <th>Aksi</th>
              </tr>
            </thead>
            <tbody>
              {reports.map((report) => (
                <tr key={report.id}>
                  <td>
                    <div className="d-flex align-items-center">
                      <i className="bi bi-file-earmark-pdf-fill text-danger me-2"></i>
                      <div>
                        <strong>{report.file_name}</strong>
                      </div>
                    </div>
                  </td>
                  <td>
                    {getReportTypeBadge(report.report_type)}
                  </td>
                  <td>
                    {report.employee_name ? (
                      <div>
                        <strong>{report.employee_name}</strong>
                      </div>
                    ) : (
                      <span className="text-muted">N/A</span>
                    )}
                  </td>
                  <td>
                    {report.evaluation_period ? (
                      <small>
                        {new Date(report.evaluation_period.start).toLocaleDateString('id-ID')} - 
                        {new Date(report.evaluation_period.end).toLocaleDateString('id-ID')}
                      </small>
                    ) : (
                      <span className="text-muted">N/A</span>
                    )}
                  </td>
                  <td>
                    <small>{report.generator_name}</small>
                  </td>
                  <td>
                    <small>{new Date(report.created_at).toLocaleDateString('id-ID')}</small>
                  </td>
                  <td>
                    <div className="d-flex gap-1">
                      <Button
                        variant="outline-success"
                        size="sm"
                        onClick={() => handleDownload(report.id, report.file_name)}
                        title="Unduh"
                      >
                        <i className="bi bi-download"></i>
                      </Button>
                      <Button
                        variant="outline-danger"
                        size="sm"
                        onClick={() => handleDelete(report.id, report.file_name)}
                        title="Hapus"
                      >
                        <i className="bi bi-trash"></i>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>

          {reports.length === 0 && !loading && (
            <div className="text-center py-4">
              <div className="display-1 text-muted mb-3">
                <i className="bi bi-file-earmark-pdf"></i>
              </div>
              <h5 className="text-muted">Belum ada laporan</h5>
              <p className="text-muted">
                Laporan akan muncul setelah Anda membuat evaluasi dan generate report
              </p>
            </div>
          )}
        </Card.Body>
      </Card>

      {/* Instructions Card */}
      <Card className="mt-4">
        <Card.Header>
          <h5 className="mb-0">
            <i className="bi bi-info-circle me-2"></i>
            Cara Membuat Laporan
          </h5>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={4}>
              <div className="text-center mb-3">
                <div className="display-6 text-primary mb-2">
                  <i className="bi bi-1-circle"></i>
                </div>
                <h6>Buat Evaluasi</h6>
                <p className="text-muted small">
                  Buat evaluasi kedisiplinan karyawan melalui form evaluasi
                </p>
              </div>
            </Col>
            <Col md={4}>
              <div className="text-center mb-3">
                <div className="display-6 text-info mb-2">
                  <i className="bi bi-2-circle"></i>
                </div>
                <h6>Lihat Hasil</h6>
                <p className="text-muted small">
                  Periksa hasil evaluasi di halaman "Hasil Evaluasi"
                </p>
              </div>
            </Col>
            <Col md={4}>
              <div className="text-center mb-3">
                <div className="display-6 text-success mb-2">
                  <i className="bi bi-3-circle"></i>
                </div>
                <h6>Generate Report</h6>
                <p className="text-muted small">
                  Klik tombol "Generate Report" untuk membuat laporan PDF
                </p>
              </div>
            </Col>
          </Row>
        </Card.Body>
      </Card>
    </Container>
  );
}

export default Reports;
